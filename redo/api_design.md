# KOL管理平台API设计文档

## 任务三：API接口设计

### 基础说明
- **API版本**: v1
- **基础URL**: `/api/v1`
- **认证方式**: <PERSON><PERSON>
- **数据格式**: JSON
- **时间格式**: ISO 8601 (YYYY-MM-DDTHH:mm:ssZ)

### 通用响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "uuid-string"
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": ["字段xxx不能为空"]
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "uuid-string"
}
```

---

## 1. 创建或更新KOL数据接口

### 1.1 批量创建/更新KOL
**请求方式**: `POST`  
**路径**: `/api/v1/projects/{project_id}/kols/batch`

#### 请求参数
- **路径参数**:
  - `project_id` (integer): 项目ID

- **请求体**:
```json
{
  "kols": [
    {
      "kol_id": "tiktok_user123",
      "kol_name": "John Doe",
      "username": "@johndoe",
      "email": "<EMAIL>",
      "bio": "Content creator focusing on tech reviews",
      "account_link": "https://tiktok.com/@johndoe",
      "followers_count": 150000,
      "following_count": 1200,
      "platform": "tiktok",
      "country": "US",
      "language": "en",
      "source": "modash",
      "engagement_rate": 3.5,
      "average_views_k": 25.5,
      "average_likes_k": 2.8,
      "average_comments_k": 0.15,
      "most_used_hashtags": ["#tech", "#review", "#gadgets"],
      "tags": [
        {
          "tag_id": 1,
          "confidence_score": 0.95,
          "source": "auto"
        }
      ]
    }
  ],
  "update_mode": "upsert"
}
```

#### 响应格式
```json
{
  "success": true,
  "data": {
    "created_count": 5,
    "updated_count": 3,
    "failed_count": 0,
    "results": [
      {
        "kol_id": "tiktok_user123",
        "status": "created",
        "message": "KOL创建成功"
      }
    ]
  },
  "message": "批量操作完成",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 1.2 单个KOL创建/更新
**请求方式**: `PUT`  
**路径**: `/api/v1/projects/{project_id}/kols/{kol_id}`

#### 请求参数
- **路径参数**:
  - `project_id` (integer): 项目ID
  - `kol_id` (string): KOL唯一标识

- **请求体**:
```json
{
  "kol_name": "John Doe",
  "username": "@johndoe",
  "email": "<EMAIL>",
  "bio": "Content creator focusing on tech reviews",
  "account_link": "https://tiktok.com/@johndoe",
  "followers_count": 150000,
  "platform": "tiktok",
  "country": "US",
  "language": "en",
  "engagement_rate": 3.5,
  "most_used_hashtags": ["#tech", "#review", "#gadgets"],
  "status": "email_found",
  "tags": [1, 2, 3]
}
```

#### 响应格式
```json
{
  "success": true,
  "data": {
    "kol_id": "tiktok_user123",
    "kol_name": "John Doe",
    "username": "@johndoe",
    "email": "<EMAIL>",
    "platform": "tiktok",
    "country": "US",
    "followers_count": 150000,
    "engagement_rate": 3.5,
    "status": "email_found",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z",
    "tags": [
      {
        "id": 1,
        "name": "Gaming",
        "category": "Content"
      }
    ]
  },
  "message": "KOL信息更新成功"
}
```

---

## 2. 查询KOL数据接口

### 2.1 筛选查询KOL列表
**请求方式**: `GET`  
**路径**: `/api/v1/projects/{project_id}/kols`

#### 请求参数
- **路径参数**:
  - `project_id` (integer): 项目ID

- **查询参数**:
  - `page` (integer, 默认1): 页码
  - `page_size` (integer, 默认20): 每页数量
  - `platform` (string): 平台筛选 (tiktok, instagram, youtube)
  - `country` (string): 国家筛选 (US, JP, CN)
  - `language` (string): 语言筛选 (en, ja, zh)
  - `tags` (string): 标签筛选，逗号分隔 (gaming,beauty)
  - `status` (string): 状态筛选
  - `min_followers` (integer): 最小粉丝数
  - `max_followers` (integer): 最大粉丝数
  - `min_engagement` (float): 最小互动率
  - `max_engagement` (float): 最大互动率
  - `has_email` (boolean): 是否有邮箱
  - `hashtag` (string): 按hashtag搜索
  - `search` (string): 关键词搜索（姓名、用户名）
  - `sort_by` (string): 排序字段 (followers_count, engagement_rate, created_at)
  - `sort_order` (string): 排序方向 (asc, desc)

#### 响应格式
```json
{
  "success": true,
  "data": {
    "kols": [
      {
        "kol_id": "tiktok_user123",
        "kol_name": "John Doe",
        "username": "@johndoe",
        "email": "<EMAIL>",
        "platform": "tiktok",
        "country": "US",
        "language": "en",
        "followers_count": 150000,
        "engagement_rate": 3.5,
        "status": "email_found",
        "tags": [
          {
            "id": 1,
            "name": "Gaming",
            "category": "Content"
          }
        ],
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total_count": 156,
      "total_pages": 8,
      "has_next": true,
      "has_prev": false
    },
    "filters_applied": {
      "country": "US",
      "tags": ["gaming", "beauty"],
      "min_followers": 10000
    }
  },
  "message": "查询成功"
}
```

### 2.2 KOL详情查询
**请求方式**: `GET`  
**路径**: `/api/v1/projects/{project_id}/kols/{kol_id}`

#### 请求参数
- **路径参数**:
  - `project_id` (integer): 项目ID
  - `kol_id` (string): KOL唯一标识

- **查询参数**:
  - `include_videos` (boolean, 默认false): 是否包含视频信息
  - `include_analysis` (boolean, 默认false): 是否包含LLM分析结果
  - `include_collaborations` (boolean, 默认false): 是否包含合作记录

#### 响应格式
```json
{
  "success": true,
  "data": {
    "kol_id": "tiktok_user123",
    "kol_name": "John Doe",
    "username": "@johndoe",
    "email": "<EMAIL>",
    "bio": "Content creator focusing on tech reviews",
    "account_link": "https://tiktok.com/@johndoe",
    "followers_count": 150000,
    "following_count": 1200,
    "platform": "tiktok",
    "country": "US",
    "language": "en",
    "engagement_rate": 3.5,
    "average_views_k": 25.5,
    "average_likes_k": 2.8,
    "average_comments_k": 0.15,
    "most_used_hashtags": ["#tech", "#review", "#gadgets"],
    "status": "email_found",
    "email_extraction_status": "found",
    "llm_analysis_status": "approved",
    "tags": [
      {
        "id": 1,
        "name": "Gaming",
        "category": "Content",
        "confidence_score": 0.95
      }
    ],
    "videos": [
      {
        "video_id": "video123",
        "title": "Latest Tech Review",
        "views_count": 45000,
        "likes_count": 3200,
        "comments_count": 156,
        "published_at": "2024-01-01T10:00:00Z"
      }
    ],
    "llm_analysis": [
      {
        "analysis_type": "brand_fit",
        "analysis_result": {
          "score": 0.85,
          "reasons": ["High engagement in tech category", "Audience matches target demographic"]
        },
        "created_at": "2024-01-01T11:00:00Z"
      }
    ],
    "collaborations": [
      {
        "id": 1,
        "collaboration_type": "post",
        "status": "completed",
        "contract_amount": 500.00,
        "total_views": 50000,
        "roi": 2.5
      }
    ],
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  },
  "message": "查询成功"
}
```

---

## 3. 记录邮件发送日志接口

### 3.1 创建邮件发送记录
**请求方式**: `POST`  
**路径**: `/api/v1/email-logs`

#### 请求参数
- **请求体**:
```json
{
  "kol_id": "tiktok_user123",
  "template_id": 1,
  "from_email": "<EMAIL>",
  "to_email": "<EMAIL>",
  "subject": "Collaboration Opportunity with TechBrand",
  "content": "Hi John, We are interested in collaborating...",
  "platform": "email",
  "campaign_id": "campaign_2024_q1"
}
```

#### 响应格式
```json
{
  "success": true,
  "data": {
    "id": 12345,
    "kol_id": "tiktok_user123",
    "template_id": 1,
    "from_email": "<EMAIL>",
    "to_email": "<EMAIL>",
    "subject": "Collaboration Opportunity with TechBrand",
    "send_status": "pending",
    "success": false,
    "platform": "email",
    "campaign_id": "campaign_2024_q1",
    "created_at": "2024-01-01T12:00:00Z"
  },
  "message": "邮件发送记录创建成功"
}
```

### 3.2 更新邮件发送状态
**请求方式**: `PUT`  
**路径**: `/api/v1/email-logs/{log_id}/status`

#### 请求参数
- **路径参数**:
  - `log_id` (integer): 邮件日志ID

- **请求体**:
```json
{
  "send_status": "delivered",
  "success": true,
  "delivered_at": "2024-01-01T12:05:00Z",
  "error_message": null
}
```

#### 响应格式
```json
{
  "success": true,
  "data": {
    "id": 12345,
    "send_status": "delivered",
    "success": true,
    "delivered_at": "2024-01-01T12:05:00Z",
    "updated_at": "2024-01-01T12:05:00Z"
  },
  "message": "邮件状态更新成功"
}
```

### 3.3 批量创建邮件发送记录
**请求方式**: `POST`  
**路径**: `/api/v1/email-logs/batch`

#### 请求参数
- **请求体**:
```json
{
  "emails": [
    {
      "kol_id": "tiktok_user123",
      "template_id": 1,
      "from_email": "<EMAIL>",
      "to_email": "<EMAIL>",
      "subject": "Collaboration Opportunity",
      "campaign_id": "campaign_2024_q1"
    },
    {
      "kol_id": "tiktok_user456",
      "template_id": 1,
      "from_email": "<EMAIL>",
      "to_email": "<EMAIL>",
      "subject": "Collaboration Opportunity",
      "campaign_id": "campaign_2024_q1"
    }
  ]
}
```

#### 响应格式
```json
{
  "success": true,
  "data": {
    "created_count": 2,
    "failed_count": 0,
    "results": [
      {
        "id": 12345,
        "kol_id": "tiktok_user123",
        "status": "created"
      },
      {
        "id": 12346,
        "kol_id": "tiktok_user456",
        "status": "created"
      }
    ]
  },
  "message": "批量邮件记录创建成功"
}
```

---

## 4. 查询邮件发送记录接口

### 4.1 查询KOL的邮件发送记录
**请求方式**: `GET`  
**路径**: `/api/v1/kols/{kol_id}/email-logs`

#### 请求参数
- **路径参数**:
  - `kol_id` (string): KOL唯一标识

- **查询参数**:
  - `page` (integer, 默认1): 页码
  - `page_size` (integer, 默认20): 每页数量
  - `status` (string): 状态筛选 (pending, sent, delivered, opened, replied)
  - `template_id` (integer): 模板ID筛选
  - `campaign_id` (string): 活动ID筛选
  - `date_from` (string): 开始日期 (YYYY-MM-DD)
  - `date_to` (string): 结束日期 (YYYY-MM-DD)
  - `sort_by` (string): 排序字段 (sent_at, created_at)
  - `sort_order` (string): 排序方向 (asc, desc)

#### 响应格式
```json
{
  "success": true,
  "data": {
    "kol_info": {
      "kol_id": "tiktok_user123",
      "kol_name": "John Doe",
      "email": "<EMAIL>"
    },
    "email_logs": [
      {
        "id": 12345,
        "template_id": 1,
        "template_name": "基础合作邀请",
        "from_email": "<EMAIL>",
        "to_email": "<EMAIL>",
        "subject": "Collaboration Opportunity with TechBrand",
        "send_status": "delivered",
        "success": true,
        "sent_at": "2024-01-01T12:00:00Z",
        "delivered_at": "2024-01-01T12:05:00Z",
        "opened_at": "2024-01-01T14:30:00Z",
        "campaign_id": "campaign_2024_q1",
        "created_at": "2024-01-01T12:00:00Z"
      },
      {
        "id": 12346,
        "template_id": 2,
        "template_name": "跟进邮件",
        "from_email": "<EMAIL>",
        "to_email": "<EMAIL>",
        "subject": "Following up on collaboration opportunity",
        "send_status": "sent",
        "success": true,
        "sent_at": "2024-01-05T10:00:00Z",
        "campaign_id": "campaign_2024_q1",
        "created_at": "2024-01-05T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total_count": 5,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    },
    "summary": {
      "total_emails": 5,
      "sent_emails": 5,
      "delivered_emails": 4,
      "opened_emails": 2,
      "replied_emails": 1,
      "delivery_rate": 80.0,
      "open_rate": 50.0,
      "reply_rate": 25.0
    }
  },
  "message": "查询成功"
}
```

### 4.2 邮件发送统计查询
**请求方式**: `GET`  
**路径**: `/api/v1/projects/{project_id}/email-stats`

#### 请求参数
- **路径参数**:
  - `project_id` (integer): 项目ID

- **查询参数**:
  - `date_from` (string): 开始日期 (YYYY-MM-DD)
  - `date_to` (string): 结束日期 (YYYY-MM-DD)
  - `group_by` (string): 分组维度 (day, week, month, template, campaign)
  - `campaign_id` (string): 活动ID筛选
  - `template_id` (integer): 模板ID筛选

#### 响应格式
```json
{
  "success": true,
  "data": {
    "overall_stats": {
      "total_sent": 1250,
      "total_delivered": 1180,
      "total_opened": 590,
      "total_clicked": 118,
      "total_replied": 59,
      "delivery_rate": 94.4,
      "open_rate": 50.0,
      "click_rate": 10.0,
      "reply_rate": 5.0
    },
    "time_series": [
      {
        "date": "2024-01-01",
        "sent": 50,
        "delivered": 48,
        "opened": 24,
        "clicked": 5,
        "replied": 2
      },
      {
        "date": "2024-01-02",
        "sent": 75,
        "delivered": 70,
        "opened": 35,
        "clicked": 7,
        "replied": 3
      }
    ],
    "template_stats": [
      {
        "template_id": 1,
        "template_name": "基础合作邀请",
        "sent": 800,
        "delivered": 760,
        "opened": 380,
        "reply_rate": 4.5
      },
      {
        "template_id": 2,
        "template_name": "跟进邮件",
        "sent": 450,
        "delivered": 420,
        "opened": 210,
        "reply_rate": 6.2
      }
    ]
  },
  "message": "统计查询成功"
}
```

---

## 5. 额外的实用接口

### 5.1 根据hashtag反查KOL
**请求方式**: `GET`  
**路径**: `/api/v1/kols/search/hashtag`

#### 请求参数
- **查询参数**:
  - `hashtag` (string, 必填): 要搜索的hashtag
  - `project_id` (integer): 项目ID筛选
  - `platform` (string): 平台筛选
  - `min_followers` (integer): 最小粉丝数
  - `page` (integer, 默认1): 页码
  - `page_size` (integer, 默认20): 每页数量

#### 响应格式
```json
{
  "success": true,
  "data": {
    "hashtag": "#tech",
    "kols": [
      {
        "kol_id": "tiktok_user123",
        "kol_name": "John Doe",
        "platform": "tiktok",
        "followers_count": 150000,
        "engagement_rate": 3.5,
        "matching_hashtags": ["#tech", "#review", "#gadgets"],
        "hashtag_frequency": 15
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total_count": 45,
      "total_pages": 3
    }
  },
  "message": "搜索成功"
}
```

### 5.2 KOL状态批量更新
**请求方式**: `PUT`  
**路径**: `/api/v1/projects/{project_id}/kols/status/batch`

#### 请求参数
- **路径参数**:
  - `project_id` (integer): 项目ID

- **请求体**:
```json
{
  "kol_ids": ["tiktok_user123", "tiktok_user456"],
  "status": "contacted",
  "email_extraction_status": "found",
  "llm_analysis_status": "approved"
}
```

#### 响应格式
```json
{
  "success": true,
  "data": {
    "updated_count": 2,
    "failed_count": 0,
    "results": [
      {
        "kol_id": "tiktok_user123",
        "status": "updated"
      },
      {
        "kol_id": "tiktok_user456",
        "status": "updated"
      }
    ]
  },
  "message": "批量状态更新成功"
}
```

---

## 错误代码定义

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| DUPLICATE_RESOURCE | 409 | 资源已存在 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| DATABASE_ERROR | 500 | 数据库操作失败 |
| EXTERNAL_API_ERROR | 502 | 外部API调用失败 |

---

## 认证和权限

### 认证方式
- 使用Bearer Token进行身份验证
- Token需要在请求头中携带：`Authorization: Bearer <token>`

### 权限控制
- **项目级权限**: 用户只能访问自己有权限的项目数据
- **操作级权限**: 读取、创建、更新、删除操作需要对应权限
- **数据隔离**: 不同项目的数据完全隔离

### 请求限制
- 单个用户每分钟最多100个请求
- 批量操作单次最多处理1000条记录
- 文件上传最大10MB 