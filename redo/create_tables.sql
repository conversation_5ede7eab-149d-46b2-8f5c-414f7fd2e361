
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; --提升文本搜索性能

-- 1. 项目表
CREATE TABLE projects (
    code VARCHAR(50) PRIMARY KEY, -- 项目唯一标识符
    name VARCHAR(255) NOT NULL, -- 项目名称
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() -- 更新时间
);

-- 2. 邮件模板表：postmark模板

CREATE TABLE email_templates (
    code VARCHAR(50) PRIMARY KEY, -- 模板唯一代码
    name VARCHAR(255) NOT NULL, -- 模板名称
    project_code VARCHAR(50) REFERENCES projects(code) ON DELETE CASCADE, -- 项目编码
    postmark_token VARCHAR(255) NULL, -- 邮件服务提供商的令牌
    from_email VARCHAR(255) NULL, -- 发件人邮箱
    note TEXT NULL, -- 备注
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() -- 更新时间
);

-- 3. 爬虫任务表
CREATE TABLE crawler_tasks (
    id SERIAL PRIMARY KEY, 
    task_name VARCHAR(255) NOT NULL,  -- 任务名称
    source VARCHAR(255) NOT NULL, -- 数据源
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('tiktok', 'instagram', 'youtube')), -- 爬取平台
    project_code VARCHAR(50) REFERENCES projects(code) ON DELETE CASCADE, -- 项目编码
    
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')), -- 任务状态
    
    filters JSONB DEFAULT '{}',  -- 过滤条件JSON
    cookies TEXT, -- 爬虫cookie
    
    log_msg TEXT,  -- 日志信息，包含爬取条数crawl_count、有邮箱的条数has_email_count，执行结果信息
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() -- 更新时间
);

-- 4. KOL信息表
CREATE TABLE kols (
    id SERIAL PRIMARY KEY, 
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('tiktok', 'instagram', 'youtube')), -- KOL所在平台
    kol_id VARCHAR(255) UNIQUE NOT NULL, -- KOL的唯一标识符（例如 TikTok 的 link_id）
    nick_name VARCHAR(255) NOT NULL, -- KOL昵称
    project_code VARCHAR(50) REFERENCES projects(code) ON DELETE CASCADE, -- 项目编码
    email VARCHAR(255), -- KOL邮箱
    bio TEXT, -- KOL简介
    
    followers_count BIGINT, -- 粉丝数
    likes_count BIGINT, -- 点赞数
    source VARCHAR(50), -- KOL来源
    
    engagement_rate DECIMAL(8,6), -- 互动率
    mean_views_k DECIMAL(12,3), -- 平均观看数（千次）
    median_views_k DECIMAL(12,3), -- 中位观看数（千次）
    
    tier VARCHAR(20) CHECK (tier IN ('nano', 'micro', 'mid', 'macro', 'mega')), -- KOL分级
    
    -- 配合trgm插件，gin索引 根据复杂度降低至O(log(n))
    hashtags JSONB DEFAULT '[]',  -- 存储hashtag数组 
    captions JSONB DEFAULT '[]',  -- 存储caption数组
    topics JSONB DEFAULT '[]',  -- 存储topic数组
    
    crawler_task_id INTEGER REFERENCES crawler_tasks(id) ON DELETE SET NULL, -- 爬虫任务ID 出现数据问题时，能够知道是哪个爬虫任务的问题
    note TEXT NULL, -- 备注
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() -- 更新时间
);



-- 5. 邮件发送表，记录了所有的邮件发送记录
CREATE TABLE email_send (
    id SERIAL PRIMARY KEY, 
    platform VARCHAR(20) CHECK (platform IN ('tiktok', 'instagram', 'youtube')), -- 邮件发送的平台
    kol_id VARCHAR(255) NOT NULL REFERENCES kols(kol_id) ON DELETE SET NULL, -- KOL ID（外键）
    send_status VARCHAR(20) NOT NULL CHECK (send_status IN ('pending', 'sent', 'failed')), -- 邮件发送状态
    send_date TIMESTAMP WITH TIME ZONE NULL, -- 邮件发送时间
    project_code VARCHAR(50) REFERENCES projects(code) ON DELETE CASCADE, -- 项目编码
    template_code VARCHAR(50) REFERENCES email_templates(code) ON DELETE CASCADE, -- 邮件模板ID
    from_email VARCHAR(255) NULL, -- 发件人邮箱
    to_email VARCHAR(255) NULL, -- 收件人邮箱
    note TEXT NULL, -- 备注
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL -- 更新时间
);

-- 6. 候选人表：回复了邮件的kol，记录跟进状态
CREATE TABLE candidates (
    id SERIAL PRIMARY KEY, 
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('tiktok', 'instagram', 'youtube')), -- KOL所在平台
    kol_id VARCHAR(255) NOT NULL REFERENCES kols(kol_id) ON DELETE CASCADE, -- KOL ID（外键）
    nick_name VARCHAR(255), -- KOL的昵称
    project_code VARCHAR(50) REFERENCES projects(code) ON DELETE CASCADE, -- 项目编码
    reply_email_addr VARCHAR(255), -- 回复邮箱地址
    follow_up_status VARCHAR(50) CHECK (follow_up_status IN ('pending', 'processing', 'drafting', 'completed', 'paid')), -- 跟进状态
    follow_up_note TEXT, -- 跟进备注
    email_send_status VARCHAR(255) NULL, -- 邮件发送状态
    latest_email_send_id INTEGER REFERENCES email_send(id) ON DELETE SET NULL, -- 最新邮件发送ID
    thread_id VARCHAR(255) not NULL, -- 关联邮件线程ID
    tracker VARCHAR(50), -- 分配给的跟进人
    note TEXT NULL, -- 备注
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL -- 更新时间
);

-- 7. kol单条视频合作绩效表
CREATE TABLE performance (
    id SERIAL PRIMARY KEY,  
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('tiktok', 'instagram', 'youtube')), -- KOL所在平台
    kol_id VARCHAR(255) NOT NULL REFERENCES kols(kol_id) ON DELETE CASCADE, -- KOL ID（外键）
    project_code VARCHAR(50) REFERENCES projects(code) ON DELETE CASCADE, -- 项目编码
    cpm DECIMAL(10,4) NULL, -- 每千次曝光费用
    post_link VARCHAR(500) UNIQUE NOT NULL, -- 帖子链接
    post_date TIMESTAMP WITH TIME ZONE NULL, -- 发布日期
    views_total BIGINT NULL, -- 总观看数
    likes_total BIGINT NULL, -- 总点赞数
    comments_total BIGINT NULL, -- 总评论数
    shares_total BIGINT NULL, -- 总分享数
    views_day1 BIGINT NULL, -- 第一天观看数
    likes_day1 BIGINT NULL, -- 第一天点赞数
    comments_day1 BIGINT NULL, -- 第一天评论数
    shares_day1 BIGINT NULL, -- 第一天分享数
    views_day3 BIGINT NULL, -- 第三天观看数
    likes_day3 BIGINT NULL, -- 第三天点赞数
    comments_day3 BIGINT NULL, -- 第三天评论数
    shares_day3 BIGINT NULL, -- 第三天分享数
    views_day7 BIGINT NULL, -- 第七天观看数
    likes_day7 BIGINT NULL, -- 第七天点赞数
    comments_day7 BIGINT NULL, -- 第七天评论数
    shares_day7 BIGINT NULL, -- 第七天分享数
    

    -- 支付信息
    payment DECIMAL(10,2) NULL, -- 支付金额
    paypal_accounts VARCHAR(255) NULL, -- PayPal账户
    tracker VARCHAR(50) NULL, -- 跟进人
    payout_date TIMESTAMP WITH TIME ZONE NULL, -- 支付日期
    fund_source VARCHAR(100) NULL, -- 资金来源

    note TEXT NULL, -- 备注

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL, -- 更新时间
);

COMMENT ON DATABASE postgres IS 'KOL管理平台数据库 - 版本1.0'; 


--爬虫入库-->解析filters字段，生成基础信息、hashtags、captions、topics字段---》kols表

