





-- 为KOL表创建优化的索引，支持多维度筛选查询

-- 基础信息索引
CREATE INDEX idx_kols_link_id_platform ON kols(link_id, platform);
CREATE INDEX idx_kols_project_platform ON kols(project_id, platform);
CREATE INDEX idx_kols_tier ON kols(tier);
CREATE INDEX idx_kols_source ON kols(source);

-- 基础指标范围查询索引
CREATE INDEX idx_kols_followers_count ON kols(followers_count) WHERE followers_count IS NOT NULL;
CREATE INDEX idx_kols_likes_count ON kols(likes_count) WHERE likes_count IS NOT NULL;
CREATE INDEX idx_kols_engagement_rate ON kols(engagement_rate) WHERE engagement_rate IS NOT NULL;

-- 性能指标索引
CREATE INDEX idx_kols_mean_views ON kols(mean_views_k) WHERE mean_views_k IS NOT NULL;
CREATE INDEX idx_kols_median_views ON kols(median_views_k) WHERE median_views_k IS NOT NULL;

-- 组合索引用于常见查询场景
CREATE INDEX idx_kols_project_tier_platform ON kols(project_id, tier, platform);
CREATE INDEX idx_kols_project_followers_range ON kols(project_id, followers_count) WHERE followers_count IS NOT NULL;
CREATE INDEX idx_kols_tier_followers ON kols(tier, followers_count) WHERE tier IS NOT NULL AND followers_count IS NOT NULL;

-- JSONB字段的GIN索引，支持标签查询
CREATE INDEX idx_kols_hashtags_gin ON kols USING GIN (hashtags);
CREATE INDEX idx_kols_captions_gin ON kols USING GIN (captions);
CREATE INDEX idx_kols_topics_gin ON kols USING GIN (topics);

-- 文本搜索索引
CREATE INDEX idx_kols_nick_name_trgm ON kols USING GIN (nick_name gin_trgm_ops);
CREATE INDEX idx_kols_bio_trgm ON kols USING GIN (bio gin_trgm_ops) WHERE bio IS NOT NULL;

-- 时间戳索引
CREATE INDEX idx_kols_created_at ON kols(created_at);
CREATE INDEX idx_kols_updated_at ON kols(updated_at);

-- 爬虫任务关联索引
CREATE INDEX idx_kols_crawler_task ON kols(crawler_task_id) WHERE crawler_task_id IS NOT NULL;


-- 为email_send表创建索引，支持多维度查询和统计

-- 基础查询索引
CREATE INDEX idx_email_send_kol_id ON email_send(kol_id);
CREATE INDEX idx_email_send_project_id ON email_send(project_id);
CREATE INDEX idx_email_send_template_id ON email_send(template_id);
CREATE INDEX idx_email_send_platform ON email_send(platform);

-- 发送状态和轮次索引
CREATE INDEX idx_email_send_send_status ON email_send(send_status);
CREATE INDEX idx_email_send_send_round ON email_send(send_round);

-- 时间范围查询索引
CREATE INDEX idx_email_send_send_date ON email_send(send_date);
CREATE INDEX idx_email_send_created_at ON email_send(created_at);
CREATE INDEX idx_email_send_updated_at ON email_send(updated_at);

-- 组合索引用于常见查询场景
CREATE INDEX idx_email_send_project_status ON email_send(project_id, send_status);
CREATE INDEX idx_email_send_project_round ON email_send(project_id, send_round);
CREATE INDEX idx_email_send_project_date ON email_send(project_id, send_date) WHERE send_date IS NOT NULL;
CREATE INDEX idx_email_send_status_date ON email_send(send_status, send_date) WHERE send_date IS NOT NULL;

-- 统计分析专用索引
CREATE INDEX idx_email_send_project_platform_status ON email_send(project_id, platform, send_status);
CREATE INDEX idx_email_send_round_status_date ON email_send(send_round, send_status, send_date) WHERE send_date IS NOT NULL;

-- 邮件地址索引
CREATE INDEX idx_email_send_from_email ON email_send(from_email) WHERE from_email IS NOT NULL;
CREATE INDEX idx_email_send_to_email ON email_send(to_email) WHERE to_email IS NOT NULL;



-- 为candidates表创建索引，支持多维度筛选查询

-- 基础查询索引
CREATE INDEX idx_candidates_kol_id ON candidates(kol_id);
CREATE INDEX idx_candidates_project_id ON candidates(project_id);
CREATE INDEX idx_candidates_platform ON candidates(platform);
CREATE INDEX idx_candidates_follow_up_status ON candidates(follow_up_status);
CREATE INDEX idx_candidates_follower ON candidates(follower) WHERE follower IS NOT NULL;

-- 组合索引用于常见查询场景
CREATE INDEX idx_candidates_project_platform ON candidates(project_id, platform);
CREATE INDEX idx_candidates_project_status ON candidates(project_id, follow_up_status);
CREATE INDEX idx_candidates_project_follower ON candidates(project_id, follower) WHERE follower IS NOT NULL;
CREATE INDEX idx_candidates_platform_status ON candidates(platform, follow_up_status);
CREATE INDEX idx_candidates_follower_status ON candidates(follower, follow_up_status) WHERE follower IS NOT NULL;

-- 三维组合索引用于复杂筛选
CREATE INDEX idx_candidates_project_platform_status ON candidates(project_id, platform, follow_up_status);
CREATE INDEX idx_candidates_project_follower_status ON candidates(project_id, follower, follow_up_status) WHERE follower IS NOT NULL;

-- 时间戳索引
CREATE INDEX idx_candidates_created_at ON candidates(created_at);
CREATE INDEX idx_candidates_updated_at ON candidates(updated_at);

-- 邮件地址索引
CREATE INDEX idx_candidates_reply_email ON candidates(reply_email_addr) WHERE reply_email_addr IS NOT NULL;

-- 昵称文本搜索索引
CREATE INDEX idx_candidates_kol_nick_name_trgm ON candidates USING GIN (kol_nick_name gin_trgm_ops) WHERE kol_nick_name IS NOT NULL;


-- 为performance表创建索引
CREATE INDEX idx_performance_kol_id ON performance USING btree (kol_id);
CREATE INDEX idx_performance_project_id ON performance USING btree (project_id);
CREATE INDEX idx_performance_post_date ON performance USING btree (post_date);
CREATE INDEX idx_performance_created_at ON performance USING btree (created_at);
CREATE INDEX idx_performance_updated_at ON performance USING btree (updated_at);
CREATE INDEX idx_performance_payout_date ON performance USING btree (payout_date);

-- 复合索引用于常见查询组合
CREATE INDEX idx_performance_project_kol ON performance USING btree (project_id, kol_id);
CREATE INDEX idx_performance_kol_post_date ON performance USING btree (kol_id, post_date);
CREATE INDEX idx_performance_project_post_date ON performance USING btree (project_id, post_date);

-- 跟进人索引（用于筛选特定跟进人的绩效数据）
CREATE INDEX idx_performance_follower ON performance USING btree (follower) WHERE follower IS NOT NULL;

-- 支付相关索引
CREATE INDEX idx_performance_payment ON performance USING btree (payment) WHERE payment IS NOT NULL;
CREATE INDEX idx_performance_fund_source ON performance USING btree (fund_source) WHERE fund_source IS NOT NULL;

-- 绩效指标范围查询索引
CREATE INDEX idx_performance_engagement_rate ON performance USING btree (engagement_rate) WHERE engagement_rate IS NOT NULL;
CREATE INDEX idx_performance_views_total ON performance USING btree (views_total) WHERE views_total IS NOT NULL;


-- 为crawler_tasks表创建索引
CREATE INDEX idx_crawler_tasks_platform ON crawler_tasks USING btree (platform);
CREATE INDEX idx_crawler_tasks_project_id ON crawler_tasks USING btree (project_id);
CREATE INDEX idx_crawler_tasks_status ON crawler_tasks USING btree (status);
CREATE INDEX idx_crawler_tasks_created_at ON crawler_tasks USING btree (created_at);
CREATE INDEX idx_crawler_tasks_updated_at ON crawler_tasks USING btree (updated_at);
CREATE INDEX idx_crawler_tasks_started_at ON crawler_tasks USING btree (started_at);
CREATE INDEX idx_crawler_tasks_completed_at ON crawler_tasks USING btree (completed_at);

-- 复合索引用于常见查询组合
CREATE INDEX idx_crawler_tasks_project_platform ON crawler_tasks USING btree (project_id, platform);
CREATE INDEX idx_crawler_tasks_platform_status ON crawler_tasks USING btree (platform, status);
CREATE INDEX idx_crawler_tasks_project_status ON crawler_tasks USING btree (project_id, status);

-- 任务名称索引（用于按名称搜索任务）
CREATE INDEX idx_crawler_tasks_task_name ON crawler_tasks USING btree (task_name);

-- 时间范围查询索引
CREATE INDEX idx_crawler_tasks_time_range ON crawler_tasks USING btree (started_at, completed_at);

-- 爬取结果统计索引
CREATE INDEX idx_crawler_tasks_cnt ON crawler_tasks USING btree (cnt) WHERE cnt > 0;

-- JSONB字段索引（用于过滤条件查询）
CREATE INDEX idx_crawler_tasks_filters ON crawler_tasks USING gin (filters);
