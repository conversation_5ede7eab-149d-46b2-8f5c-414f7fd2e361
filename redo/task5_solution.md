# 任务五解决方案：KOL信息表重新设计

## 问题分析

### 1. 老系统Filter问题
原系统使用简单字符串命名filter（如"Modash-dabi related-hashtags-10k-1849"），导致：
- 无法精确反查KOL的筛选条件
- 标签信息模糊，无法准确识别KOL特征
- 数据追溯困难，无法分析筛选效果

### 2. 字段冗余问题
从老数据分析发现的问题：
- `most_used_hashtags` 存储为JSON字符串，查询效率低
- `average_*` 字段与 `mean_*` 字段重复
- `slug` 和 `creator_id` 字段使用率低，部分为null
- 数据类型不合理（如engagement_rate精度不足）

## 解决方案

### 1. 重新设计筛选条件系统

#### A. 创建详细的筛选条件记录表
```sql
CREATE TABLE filter_conditions (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id),
    filter_name VARCHAR(255) NOT NULL,
    source_platform VARCHAR(50) NOT NULL, -- modash, ttone, creable等
    
    -- 详细筛选条件
    target_platform VARCHAR(50),
    min_followers BIGINT,
    max_followers BIGINT,
    countries TEXT[], -- 数组存储多个国家
    languages TEXT[], -- 数组存储多个语言
    hashtags TEXT[], -- 数组存储hashtag
    keywords TEXT[], -- 数组存储关键词
    min_engagement_rate DECIMAL(5,4),
    max_engagement_rate DECIMAL(5,4),
    age_range VARCHAR(20),
    gender VARCHAR(20),
    
    -- 其他灵活条件
    additional_filters JSONB,
    
    -- 统计信息
    total_results INTEGER DEFAULT 0,
    imported_count INTEGER DEFAULT 0,
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### B. 创建KOL与筛选条件关联表
```sql
CREATE TABLE kol_filter_associations (
    id SERIAL PRIMARY KEY,
    kol_id VARCHAR(255) REFERENCES kol_info(kol_id),
    filter_condition_id INTEGER REFERENCES filter_conditions(id),
    import_batch_id VARCHAR(100), -- 导入批次ID
    matched_criteria JSONB, -- 记录匹配的具体条件
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. 优化标签系统

#### A. 重新设计标签分类
```sql
CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(100) CHECK (category IN ('content', 'demographic', 'performance', 'industry', 'custom')),
    color VARCHAR(7),
    is_active BOOLEAN DEFAULT true
);
```

#### B. 分离hashtag存储
```sql
CREATE TABLE kol_hashtags (
    id SERIAL PRIMARY KEY,
    kol_id VARCHAR(255) REFERENCES kol_info(kol_id),
    hashtag VARCHAR(255) NOT NULL,
    frequency INTEGER DEFAULT 1,
    last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source VARCHAR(50) DEFAULT 'extracted',
    UNIQUE(kol_id, hashtag)
);
```

### 3. 优化KOL信息表结构

#### A. 删除冗余字段
删除以下字段：
- `average_views_k` (与mean_views_k重复)
- `average_likes_k` (与mean_views_k重复)
- `average_comments_k` (与mean_views_k重复)
- `most_used_hashtags` (移至独立表)
- `slug` (使用率低)
- `creator_id` (使用率低)

#### B. 优化字段类型
```sql
-- 修改前
engagement_rate DECIMAL(5,2)  -- 精度不足
followers_k DECIMAL(10,2)     -- 字段名不规范

-- 修改后
engagement_rate DECIMAL(8,6)  -- 提高精度
followers_count BIGINT        -- 规范字段名，使用整数
likes_count BIGINT           -- 新增总点赞数
mean_views_k DECIMAL(12,3)   -- 保留平均观看数
median_views_k DECIMAL(12,3) -- 保留中位数观看数
tier VARCHAR(50)             -- 新增KOL分级字段
```

### 4. 数据规范化改进

#### A. 第一范式 (1NF)
- 将`most_used_hashtags`从JSON字符串拆分为独立表
- 将`countries`和`languages`在filter表中使用数组存储

#### B. 第二范式 (2NF)
- 筛选条件独立为`filter_conditions`表
- KOL与筛选条件的关联独立为`kol_filter_associations`表

#### C. 第三范式 (3NF)
- 标签信息独立为`tags`表
- hashtag信息独立为`kol_hashtags`表

## 业务价值

### 1. 精确的数据追溯
- 可以准确知道每个KOL来自哪个筛选条件
- 可以分析不同筛选条件的效果
- 支持批量导入的追溯管理

### 2. 高效的反查功能
```sql
-- 根据hashtag反查KOL
SELECT k.* FROM kol_info k
JOIN kol_hashtags h ON k.kol_id = h.kol_id
WHERE h.hashtag = 'gaming';

-- 根据筛选条件反查KOL
SELECT k.* FROM kol_info k
JOIN kol_filter_associations kfa ON k.kol_id = kfa.kol_id
JOIN filter_conditions fc ON kfa.filter_condition_id = fc.id
WHERE fc.filter_name = 'Gaming-US-10k-100k';
```

### 3. 灵活的标签管理
- 支持多维度标签分类
- 支持动态标签添加
- 支持标签置信度评分

### 4. 优化的存储和查询性能
- 使用数组和GIN索引优化hashtag查询
- 规范化数据类型减少存储空间
- 添加合适的索引提高查询效率

## 迁移建议

### 1. 数据迁移脚本
```sql
-- 迁移hashtag数据
INSERT INTO kol_hashtags (kol_id, hashtag, frequency, source)
SELECT 
    kol_id,
    jsonb_array_elements_text(most_used_hashtags::jsonb) as hashtag,
    1 as frequency,
    'migrated' as source
FROM old_kol_info 
WHERE most_used_hashtags IS NOT NULL AND most_used_hashtags != 'null';

-- 迁移分级数据
UPDATE kol_info SET tier = 
    CASE 
        WHEN level LIKE '%Nano%' THEN 'nano'
        WHEN level LIKE '%Micro%' THEN 'micro'
        WHEN level LIKE '%Mid%' THEN 'mid'
        WHEN level LIKE '%Macro%' THEN 'macro'
        WHEN level LIKE '%Mega%' THEN 'mega'
        ELSE NULL
    END;
```

### 2. 应用层改动
- 更新KOL导入逻辑，记录详细筛选条件
- 修改hashtag查询接口，使用新的表结构
- 添加筛选条件效果分析功能

## 总结

通过重新设计数据库结构，我们解决了：
1. **Filter追溯问题**：详细记录每个筛选条件，支持精确反查
2. **数据冗余问题**：规范化设计，消除重复字段
3. **查询效率问题**：优化索引和数据类型，提高查询性能
4. **扩展性问题**：灵活的标签系统和JSONB字段支持未来扩展

这个设计为KOL管理平台提供了更强大的数据分析能力和更好的用户体验。 







-- 10. 创建字段映射配置表（用于管理第三方平台字段与KOL表字段的映射关系）
CREATE TABLE field_mappings (
    id SERIAL PRIMARY KEY,
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('tiktok', 'instagram', 'youtube')),
    source_field VARCHAR(100) NOT NULL,                -- 第三方平台字段名
    target_field VARCHAR(100) NOT NULL,                -- KOL表字段名
    field_type VARCHAR(20) NOT NULL CHECK (field_type IN ('string', 'integer', 'decimal', 'boolean', 'jsonb')),
    transform_rule TEXT,                                -- 转换规则（可选，用于复杂转换）
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(platform, source_field, target_field)
);

-- 插入默认的字段映射配置
INSERT INTO field_mappings (platform, source_field, target_field, field_type, transform_rule) VALUES
-- 通用字段映射
('tiktok', 'username', 'link_id', 'string', NULL),
('tiktok', 'display_name', 'nick_name', 'string', NULL),
('tiktok', 'bio', 'bio', 'string', NULL),
('tiktok', 'followers_count', 'followers_count', 'integer', NULL),
('tiktok', 'engagement_rate', 'engagement_rate', 'decimal', NULL),
('tiktok', 'hashtags', 'hashtags', 'jsonb', 'json_array'),
('tiktok', 'topics', 'topics', 'jsonb', 'json_array'),

('instagram', 'username', 'link_id', 'string', NULL),
('instagram', 'full_name', 'nick_name', 'string', NULL),
('instagram', 'biography', 'bio', 'string', NULL),
('instagram', 'followers_count', 'followers_count', 'integer', NULL),
('instagram', 'engagement_rate', 'engagement_rate', 'decimal', NULL),
('instagram', 'hashtags', 'hashtags', 'jsonb', 'json_array'),
('instagram', 'categories', 'topics', 'jsonb', 'json_array'),

('youtube', 'channel_handle', 'link_id', 'string', NULL),
('youtube', 'channel_name', 'nick_name', 'string', NULL),
('youtube', 'description', 'bio', 'string', NULL),
('youtube', 'subscriber_count', 'followers_count', 'integer', NULL),
('youtube', 'engagement_rate', 'engagement_rate', 'decimal', NULL),
('youtube', 'tags', 'hashtags', 'jsonb', 'json_array'),
('youtube', 'topics', 'topics', 'jsonb', 'json_array');

CREATE INDEX ix_field_mappings_platform ON field_mappings USING btree (platform);
CREATE INDEX ix_field_mappings_active ON field_mappings USING btree (is_active);


-- Filter条件反写到KOL表的解决方案说明：
-- 
-- 【核心思路】：建立第三方平台filter字段与kols表字段的映射关系
-- 
-- 1. 第三方平台Filter -> KOL表字段映射关系：
--    ┌─────────────────────────────┬─────────────────────────────┐
--    │ 第三方平台Filter字段         │ KOL表字段                   │
--    ├─────────────────────────────┼─────────────────────────────┤
--    │ hashtags                    │ hashtags (JSONB)            │
--    │ topics/categories           │ topics (JSONB)              │
--    │ bio/description             │ bio (TEXT)                  │
--    │ followers_count             │ followers_count (BIGINT)    │
--    │ engagement_rate             │ engagement_rate (DECIMAL)   │
--    │ tier/level                  │ tier (VARCHAR)              │
--    │ platform                    │ platform (VARCHAR)          │
--    │ username/handle             │ link_id (VARCHAR)           │
--    │ display_name                │ nick_name (VARCHAR)         │
--    │ email                       │ email (VARCHAR)             │
--    │ location                    │ 可扩展到kols表              │
--    │ gender                      │ 可扩展到kols表              │
--    └─────────────────────────────┴─────────────────────────────┘
-- 
-- 2. 数据反写实现流程：
--    Step 1: 爬虫根据original_filters获取第三方平台数据
--    Step 2: 解析返回的KOL数据，提取关键字段
--    Step 3: 根据映射关系将数据写入kols表对应字段
--    Step 4: 设置crawler_task_id建立关联关系
-- 
-- 3. 示例original_filters格式：
-- {
--   "search_criteria": {
--     "hashtags": ["#fashion", "#beauty", "#lifestyle"],
--     "topics": ["fashion", "beauty", "lifestyle"],
--     "followers_range": {"min": 10000, "max": 100000},
--     "engagement_rate_range": {"min": 0.02, "max": 0.08},
--     "location": ["US", "UK", "CA"],
--     "gender": "female",
--     "tier": "micro"
--   },
--   "platform_specific": {
--     "tiktok": {"video_count_min": 50},
--     "instagram": {"post_count_min": 100},
--     "youtube": {"subscriber_count_min": 5000}
--   }
-- }
-- 
-- 4. 数据反写完整实现示例：
-- 

-- A. 基于field_mappings表的动态映射函数：
-- ```python
-- async def get_field_mappings(platform: str, db_session):
--     """获取指定平台的字段映射配置"""
--     query = """
--         SELECT source_field, target_field, field_type, transform_rule 
--         FROM field_mappings 
--         WHERE platform = %s AND is_active = true
--     """
--     result = await db_session.execute(query, (platform,))
--     return {row['source_field']: row for row in result.fetchall()}
-- 
-- def transform_field_value(value, field_type, transform_rule):
--     """根据字段类型和转换规则处理字段值"""
--     if value is None:
--         return None
--     
--     if transform_rule == 'json_array':
--         return json.dumps(value if isinstance(value, list) else [value])
--     elif field_type == 'integer':
--         return int(value) if str(value).isdigit() else None
--     elif field_type == 'decimal':
--         return float(value) if isinstance(value, (int, float, str)) else None
--     else:
--         return str(value)
-- 
-- async def map_platform_data_to_kol(platform_data, platform, crawler_task_id, db_session):
--     """动态映射第三方平台数据到KOL表字段"""
--     # 获取字段映射配置
--     mappings = await get_field_mappings(platform, db_session)
--     
--     kol_data = {
--         'platform': platform,
--         'crawler_task_id': crawler_task_id,
--         'created_at': datetime.now(),
--         'updated_at': datetime.now()
--     }
--     
--     # 根据映射配置转换字段
--     for source_field, mapping in mappings.items():
--         if source_field in platform_data:
--             target_field = mapping['target_field']
--             field_type = mapping['field_type']
--             transform_rule = mapping['transform_rule']
--             
--             transformed_value = transform_field_value(
--                 platform_data[source_field], 
--                 field_type, 
--                 transform_rule
--             )
--             kol_data[target_field] = transformed_value
--     
--     # 自动计算tier等级
--     if 'followers_count' in kol_data:
--         kol_data['tier'] = determine_tier(kol_data['followers_count'])
--     
--     return kol_data
-- 
-- def determine_tier(followers_count):
--     """根据粉丝数量确定KOL等级"""
--     if followers_count is None:
--         return None
--     elif followers_count < 1000:
--         return 'nano'
--     elif followers_count < 10000:
--         return 'micro'
--     elif followers_count < 100000:
--         return 'mid'
--     elif followers_count < 1000000:
--         return 'macro'
--     else:
--         return 'mega'
-- ```
-- 
-- B. 爬虫任务执行完整流程：
-- ```python
-- async def execute_crawler_task(task_id, db_session):
--     """执行爬虫任务的完整流程"""
--     # 1. 获取任务信息
--     task = await get_crawler_task(task_id, db_session)
--     
--     # 2. 更新任务状态为running
--     await update_task_status(task_id, 'running', db_session)
--     
--     try:
--         # 3. 根据original_filters和cookies调用第三方平台API
--         platform_data_list = await call_third_party_api(
--             platform=task['platform'],
--             filters=task['original_filters'],
--             cookies=task['cookies']
--         )
--         
--         # 4. 批量处理和入库
--         kol_records = []
--         for platform_data in platform_data_list:
--             kol_data = await map_platform_data_to_kol(
--                 platform_data, 
--                 task['platform'], 
--                 task_id, 
--                 db_session
--             )
--             kol_records.append(kol_data)
--         
--         # 5. 批量插入KOL数据
--         await batch_insert_kols(kol_records, db_session)
--         
--         # 6. 更新任务状态为completed
--         await update_task_status(
--             task_id, 
--             'completed', 
--             db_session,
--             cnt=len(kol_records),
--             log_message=f"成功爬取{len(kol_records)}条KOL数据"
--         )
--         
--     except Exception as e:
--         # 7. 错误处理
--         await update_task_status(
--             task_id, 
--             'failed', 
--             db_session,
--             error_message=str(e)
--         )
--         raise
-- ```