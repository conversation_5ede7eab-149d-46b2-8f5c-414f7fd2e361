# KOL管理平台数据库设计

## 任务一：现有模型分析

### 当前模型优势分析
1. **完善的KOL信息管理**: `KOLInfo`模型包含了丰富的KOL基础信息和指标数据
2. **灵活的标签系统**: 通过`Tag`和`KolTagAssociation`实现多对多关系
3. **视频内容跟踪**: `VideoInfo`模型支持KOL作品表现监控
4. **合作跟踪**: `CollaborationPerformance`记录合作效果
5. **邮件发送记录**: `SendData`模型支持邮件发送状态跟踪

### 当前模型不足
1. **缺乏项目隔离**: 虽有`project_code`字段，但缺乏独立的项目管理表
2. **邮件模板管理**: 缺乏邮件模板的结构化管理
3. **KOL状态流转**: 缺乏明确的KOL处理状态管理
4. **LLM分析记录**: 缺乏LLM分析结果的存储

## 任务二：优化后的数据库结构设计

### 核心表设计

#### 1. 项目表 (projects)
```sql
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    project_code VARCHAR(50) UNIQUE NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active',
    budget DECIMAL(12,2),
    start_date DATE,
    end_date DATE,
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. 筛选条件记录表 (filter_conditions)
```sql
CREATE TABLE filter_conditions (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    filter_name VARCHAR(255) NOT NULL,
    source_platform VARCHAR(50) NOT NULL,
    
    -- 详细筛选条件
    target_platform VARCHAR(50),
    min_followers BIGINT,
    max_followers BIGINT,
    countries TEXT[],
    languages TEXT[],
    hashtags TEXT[],
    keywords TEXT[],
    min_engagement_rate DECIMAL(5,4),
    max_engagement_rate DECIMAL(5,4),
    age_range VARCHAR(20),
    gender VARCHAR(20),
    
    -- 其他筛选条件
    additional_filters JSONB,
    
    -- 元数据
    created_by VARCHAR(100),
    total_results INTEGER DEFAULT 0,
    imported_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. 优化的KOL信息表 (kol_info)
```sql
CREATE TABLE kol_info (
    kol_id VARCHAR(255) PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    kol_name VARCHAR(255) NOT NULL,
    username VARCHAR(255),
    email VARCHAR(255),
    bio TEXT,
    account_link VARCHAR(500),
    
    -- 基础指标
    followers_count BIGINT,
    following_count BIGINT,
    likes_count BIGINT,
    platform VARCHAR(50) NOT NULL,
    country VARCHAR(100),
    language VARCHAR(50),
    source VARCHAR(100),
    
    -- 性能指标
    engagement_rate DECIMAL(8,6),
    mean_views_k DECIMAL(12,3),
    median_views_k DECIMAL(12,3),
    
    -- KOL状态管理
    status VARCHAR(50) DEFAULT 'pending',
    email_extraction_status VARCHAR(50) DEFAULT 'pending',
    llm_analysis_status VARCHAR(50),
    llm_analysis_result TEXT,
    
    -- 分级信息
    tier VARCHAR(50),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 4. KOL与筛选条件关联表 (kol_filter_associations)
```sql
CREATE TABLE kol_filter_associations (
    id SERIAL PRIMARY KEY,
    kol_id VARCHAR(255) REFERENCES kol_info(kol_id) ON DELETE CASCADE,
    filter_condition_id INTEGER REFERENCES filter_conditions(id) ON DELETE CASCADE,
    import_batch_id VARCHAR(100),
    matched_criteria JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(kol_id, filter_condition_id)
);
```

#### 5. KOL hashtag记录表 (kol_hashtags)
```sql
CREATE TABLE kol_hashtags (
    id SERIAL PRIMARY KEY,
    kol_id VARCHAR(255) REFERENCES kol_info(kol_id) ON DELETE CASCADE,
    hashtag VARCHAR(255) NOT NULL,
    frequency INTEGER DEFAULT 1,
    last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source VARCHAR(50) DEFAULT 'extracted',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(kol_id, hashtag)
);
```

#### 6. 邮件模板表 (email_templates)
```sql
CREATE TABLE email_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    template_code VARCHAR(100) UNIQUE NOT NULL,
    subject VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    template_type VARCHAR(50) DEFAULT 'outreach',
    variables JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 7. 优化的邮件发送记录表 (email_logs)
```sql
CREATE TABLE email_logs (
    id SERIAL PRIMARY KEY,
    kol_id VARCHAR(255) REFERENCES kol_info(kol_id) ON DELETE CASCADE,
    template_id INTEGER REFERENCES email_templates(id),
    
    -- 邮件信息
    from_email VARCHAR(255) NOT NULL,
    to_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500),
    content TEXT,
    
    -- 发送状态
    send_status VARCHAR(50) DEFAULT 'pending',
    success BOOLEAN DEFAULT false,
    error_message TEXT,
    
    -- 跟踪信息
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    replied_at TIMESTAMP WITH TIME ZONE,
    
    -- 元数据
    platform VARCHAR(50),
    campaign_id VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_email_logs_kol_id ON email_logs(kol_id);
CREATE INDEX idx_email_logs_status ON email_logs(send_status);
CREATE INDEX idx_email_logs_sent_at ON email_logs(sent_at);
```

#### 8. KOL标签关联表 (kol_tags)
```sql
CREATE TABLE kol_tags (
    id SERIAL PRIMARY KEY,
    kol_id VARCHAR(255) REFERENCES kol_info(kol_id) ON DELETE CASCADE,
    tag_id INTEGER REFERENCES tags(id) ON DELETE CASCADE,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    confidence_score DECIMAL(3,2),
    source VARCHAR(50) DEFAULT 'manual',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(kol_id, tag_id, project_id)
);
```

#### 9. 标签表 (tags)
```sql
CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(100),
    color VARCHAR(7),
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 10. 视频信息表 (videos)
```sql
CREATE TABLE videos (
    video_id VARCHAR(255) PRIMARY KEY,
    kol_id VARCHAR(255) REFERENCES kol_info(kol_id) ON DELETE CASCADE,
    
    -- 视频基本信息
    title VARCHAR(500),
    description TEXT,
    video_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    duration INTEGER,
    
    -- 互动数据
    views_count BIGINT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    comments_count BIGINT DEFAULT 0,
    shares_count BIGINT DEFAULT 0,
    
    -- 平台信息
    platform VARCHAR(50) NOT NULL,
    platform_video_id VARCHAR(255),
    
    -- 内容分析
    hashtags JSONB,
    mentions JSONB,
    
    -- 时间信息
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_videos_kol_id ON videos(kol_id);
CREATE INDEX idx_videos_platform ON videos(platform);
CREATE INDEX idx_videos_published_at ON videos(published_at);
CREATE INDEX idx_videos_hashtags ON videos USING GIN(hashtags);
```

#### 11. LLM分析记录表 (llm_analysis)
```sql
CREATE TABLE llm_analysis (
    id SERIAL PRIMARY KEY,
    kol_id VARCHAR(255) REFERENCES kol_info(kol_id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL,
    input_data JSONB,
    analysis_result JSONB,
    confidence_score DECIMAL(3,2),
    model_version VARCHAR(50),
    analysis_status VARCHAR(50) DEFAULT 'pending',
    error_message TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 12. 合作记录表 (collaborations)
```sql
CREATE TABLE collaborations (
    id SERIAL PRIMARY KEY,
    kol_id VARCHAR(255) REFERENCES kol_info(kol_id) ON DELETE CASCADE,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    
    -- 合作信息
    collaboration_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'pending',
    contract_amount DECIMAL(10,2),
    currency VARCHAR(10) DEFAULT 'USD',
    
    -- 重要日期
    contract_date DATE,
    delivery_date DATE,
    payment_date DATE,
    
    -- 交付物
    deliverables JSONB,
    content_links JSONB,
    
    -- 绩效数据
    total_views BIGINT,
    total_likes BIGINT,
    total_comments BIGINT,
    total_shares BIGINT,
    roi DECIMAL(5,2),
    
    notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### KOL状态枚举定义

```sql
-- KOL处理状态
-- pending: 待处理
-- data_fetching: 数据获取中
-- email_found: 已找到邮箱
-- email_not_found: 未找到邮箱
-- llm_analyzing: LLM分析中
-- llm_approved: LLM分析通过
-- llm_rejected: LLM分析拒绝
-- manual_review: 待人工审核
-- nano_searching: Nano搜索中
-- ready_to_contact: 准备联系
-- contacted: 已联系
-- replied: 已回复
-- collaborated: 已合作
-- archived: 已归档

-- 邮件状态
-- pending: 待发送
-- sending: 发送中
-- sent: 已发送
-- delivered: 已送达
-- opened: 已打开
-- clicked: 已点击
-- replied: 已回复
-- bounced: 退信
-- failed: 发送失败
```

## ER图 (Mermaid)

```mermaid
erDiagram
    projects ||--o{ kol_info : "has"
    projects ||--o{ collaborations : "includes"
    
    kol_info ||--o{ email_logs : "receives"
    kol_info ||--o{ videos : "creates"
    kol_info ||--o{ llm_analysis : "analyzed_by"
    kol_info ||--o{ collaborations : "participates"
    kol_info ||--o{ kol_tags : "tagged_with"
    
    tags ||--o{ kol_tags : "applied_to"
    
    email_templates ||--o{ email_logs : "used_in"
    
    projects {
        int id PK
        string project_code UK
        string project_name
        text description
        string status
        decimal budget
        date start_date
        date end_date
        string created_by
        timestamp created_at
        timestamp updated_at
    }
    
    kol_info {
        string kol_id PK
        int project_id FK
        string kol_name
        string username
        string email
        text bio
        string account_link
        bigint followers_count
        bigint following_count
        string platform
        string country
        string language
        string source
        decimal engagement_rate
        decimal average_views_k
        decimal average_likes_k
        decimal average_comments_k
        jsonb most_used_hashtags
        string status
        string email_extraction_status
        string llm_analysis_status
        text llm_analysis_result
        string slug
        string creator_id
        timestamp created_at
        timestamp updated_at
    }
    
    email_templates {
        int id PK
        string template_name
        string template_code UK
        string subject
        text content
        string template_type
        jsonb variables
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    email_logs {
        int id PK
        string kol_id FK
        int template_id FK
        string from_email
        string to_email
        string subject
        text content
        string send_status
        boolean success
        text error_message
        timestamp sent_at
        timestamp delivered_at
        timestamp opened_at
        timestamp clicked_at
        timestamp replied_at
        string platform
        string campaign_id
        timestamp created_at
        timestamp updated_at
    }
    
    tags {
        int id PK
        string name UK
        text description
        string category
        string color
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    kol_tags {
        int id PK
        string kol_id FK
        int tag_id FK
        int project_id FK
        decimal confidence_score
        string source
        timestamp created_at
    }
    
    videos {
        string video_id PK
        string kol_id FK
        string title
        text description
        string video_url
        string thumbnail_url
        int duration
        bigint views_count
        bigint likes_count
        bigint comments_count
        bigint shares_count
        string platform
        string platform_video_id
        jsonb hashtags
        jsonb mentions
        timestamp published_at
        timestamp created_at
        timestamp updated_at
    }
    
    llm_analysis {
        int id PK
        string kol_id FK
        string analysis_type
        jsonb input_data
        jsonb analysis_result
        decimal confidence_score
        string model_version
        string analysis_status
        text error_message
        timestamp created_at
        timestamp updated_at
    }
    
    collaborations {
        int id PK
        string kol_id FK
        int project_id FK
        string collaboration_type
        string status
        decimal contract_amount
        string currency
        date contract_date
        date delivery_date
        date payment_date
        jsonb deliverables
        jsonb content_links
        bigint total_views
        bigint total_likes
        bigint total_comments
        bigint total_shares
        decimal roi
        text notes
        timestamp created_at
        timestamp updated_at
    }
```

## 数据库优化建议

### 1. 索引策略
- 为常用查询字段添加复合索引
- 使用GIN索引优化JSONB字段查询
- 为时间字段添加BRIN索引（适合大数据量）

### 2. 分区策略
- 按项目ID对大表进行分区
- 按时间对日志表进行分区

### 3. 数据一致性
- 使用外键约束保证数据完整性
- 添加CHECK约束验证枚举值
- 使用触发器维护统计数据

### 4. 性能优化
- 定期VACUUM和ANALYZE
- 使用连接池减少连接开销
- 为频繁查询添加物化视图 