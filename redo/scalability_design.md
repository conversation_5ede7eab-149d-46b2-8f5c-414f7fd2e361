# KOL管理平台可扩展性设计

## 任务四：可扩展性思考与解决方案

### 背景需求
当前系统只支持邮件发送，但未来还计划：
1. 支持业务方自定义字段（如"是否合作过"、"报价范围"等）
2. 多业务团队使用平台，但数据相互隔离，权限不同

---

## 1. 自定义字段扩展方案

### 1.1 设计思路
采用**EAV模型 + JSONB存储**的混合方案，既保证灵活性又兼顾性能。

### 1.2 核心表设计

#### 自定义字段定义表 (custom_fields)
```sql
CREATE TABLE custom_fields (
    id SERIAL PRIMARY KEY,
    field_name VARCHAR(100) NOT NULL,
    field_code VARCHAR(100) NOT NULL,
    field_type VARCHAR(50) NOT NULL CHECK (field_type IN ('text', 'number', 'boolean', 'date', 'select', 'multi_select', 'json')),
    field_category VARCHAR(50) DEFAULT 'kol' CHECK (field_category IN ('kol', 'project', 'collaboration', 'email')),
    
    -- 字段配置
    field_config JSONB NOT NULL DEFAULT '{}',
    -- 示例: {"required": true, "default_value": "", "max_length": 255, "options": ["option1", "option2"]}
    
    -- 权限控制
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    is_global BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    -- 显示控制
    display_order INTEGER DEFAULT 0,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(field_code, tenant_id)
);
```

#### 自定义字段值表 (custom_field_values)
```sql
CREATE TABLE custom_field_values (
    id SERIAL PRIMARY KEY,
    field_id INTEGER REFERENCES custom_fields(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL CHECK (entity_type IN ('kol', 'project', 'collaboration', 'email')),
    entity_id VARCHAR(255) NOT NULL,
    
    -- 多类型值存储
    value_text TEXT,
    value_number DECIMAL(15,4),
    value_boolean BOOLEAN,
    value_date DATE,
    value_datetime TIMESTAMP WITH TIME ZONE,
    value_json JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(field_id, entity_type, entity_id)
);

-- 为不同类型的值创建索引
CREATE INDEX idx_custom_field_values_text ON custom_field_values(field_id, value_text);
CREATE INDEX idx_custom_field_values_number ON custom_field_values(field_id, value_number);
CREATE INDEX idx_custom_field_values_boolean ON custom_field_values(field_id, value_boolean);
CREATE INDEX idx_custom_field_values_date ON custom_field_values(field_id, value_date);
CREATE INDEX idx_custom_field_values_json ON custom_field_values USING GIN(value_json);
```

### 1.3 常见自定义字段示例

```sql
-- 插入常见的自定义字段
INSERT INTO custom_fields (field_name, field_code, field_type, field_category, field_config, display_name, description, is_global) VALUES
('是否合作过', 'has_collaborated', 'boolean', 'kol', '{"required": false, "default_value": false}', '是否合作过', '记录是否曾经与该KOL合作', true),
('报价范围', 'price_range', 'select', 'kol', '{"required": false, "options": ["$0-100", "$100-500", "$500-1000", "$1000-5000", "$5000+"]}', '报价范围', 'KOL的合作报价范围', true),
('合作意向度', 'collaboration_interest', 'number', 'kol', '{"required": false, "min_value": 1, "max_value": 10, "default_value": 5}', '合作意向度', '1-10分评价合作意向', true),
('备注信息', 'notes', 'text', 'kol', '{"required": false, "max_length": 1000}', '备注信息', '额外的备注信息', true),
('标签颜色', 'tag_color', 'select', 'kol', '{"required": false, "options": ["red", "blue", "green", "yellow", "purple"]}', '标签颜色', '用于界面显示的颜色标识', true),
('最后联系时间', 'last_contact_date', 'date', 'kol', '{"required": false}', '最后联系时间', '最后一次联系的日期', true),
('社交媒体链接', 'social_links', 'json', 'kol', '{"required": false, "schema": {"instagram": "string", "twitter": "string", "youtube": "string"}}', '社交媒体链接', '其他社交媒体平台链接', true);
```

### 1.4 KOL表扩展设计

```sql
-- 为KOL表添加自定义字段存储
ALTER TABLE kol_info ADD COLUMN custom_fields JSONB DEFAULT '{}';

-- 添加GIN索引支持自定义字段查询
CREATE INDEX idx_kol_info_custom_fields ON kol_info USING GIN(custom_fields);

-- 创建函数：获取KOL的自定义字段值
CREATE OR REPLACE FUNCTION get_kol_custom_fields(p_kol_id VARCHAR(255))
RETURNS JSONB AS $$
DECLARE
    result JSONB := '{}';
    field_record RECORD;
    field_value RECORD;
BEGIN
    -- 获取所有自定义字段定义和值
    FOR field_record IN
        SELECT cf.field_code, cf.field_type, cf.display_name, cf.field_config
        FROM custom_fields cf
        WHERE cf.field_category = 'kol' AND cf.is_active = true
        ORDER BY cf.display_order, cf.id
    LOOP
        -- 获取字段值
        SELECT * INTO field_value
        FROM custom_field_values cfv
        WHERE cfv.field_id = (SELECT id FROM custom_fields WHERE field_code = field_record.field_code)
          AND cfv.entity_type = 'kol'
          AND cfv.entity_id = p_kol_id;
        
        -- 根据字段类型选择合适的值
        IF field_value.id IS NOT NULL THEN
            CASE field_record.field_type
                WHEN 'text' THEN
                    result := result || jsonb_build_object(field_record.field_code, field_value.value_text);
                WHEN 'number' THEN
                    result := result || jsonb_build_object(field_record.field_code, field_value.value_number);
                WHEN 'boolean' THEN
                    result := result || jsonb_build_object(field_record.field_code, field_value.value_boolean);
                WHEN 'date' THEN
                    result := result || jsonb_build_object(field_record.field_code, field_value.value_date);
                WHEN 'json' THEN
                    result := result || jsonb_build_object(field_record.field_code, field_value.value_json);
                ELSE
                    result := result || jsonb_build_object(field_record.field_code, field_value.value_text);
            END CASE;
        ELSE
            -- 使用默认值
            result := result || jsonb_build_object(field_record.field_code, (field_record.field_config->>'default_value')::text);
        END IF;
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;
```

---

## 2. 多业务团队隔离方案

### 2.1 租户系统设计

#### 租户表 (tenants)
```sql
CREATE TABLE tenants (
    id SERIAL PRIMARY KEY,
    tenant_code VARCHAR(50) UNIQUE NOT NULL,
    tenant_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- 配置信息
    settings JSONB DEFAULT '{}',
    -- 示例: {"max_users": 100, "max_projects": 10, "features": ["email", "sms", "analytics"]}
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    
    -- 联系信息
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    
    -- 租户关联
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- 用户信息
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    avatar_url VARCHAR(500),
    
    -- 状态管理
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 角色权限表 (roles & permissions)
```sql
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(100) NOT NULL,
    role_code VARCHAR(100) NOT NULL,
    description TEXT,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    is_system_role BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(role_code, tenant_id)
);

CREATE TABLE permissions (
    id SERIAL PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL,
    permission_code VARCHAR(100) UNIQUE NOT NULL,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
    
    UNIQUE(role_id, permission_id)
);

CREATE TABLE user_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, role_id)
);
```

### 2.2 数据隔离实现

#### 修改现有表结构
```sql
-- 为所有核心表添加租户ID
ALTER TABLE projects ADD COLUMN tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE kol_info ADD COLUMN tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE email_logs ADD COLUMN tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE tags ADD COLUMN tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE email_templates ADD COLUMN tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE;

-- 添加租户隔离索引
CREATE INDEX idx_projects_tenant ON projects(tenant_id);
CREATE INDEX idx_kol_info_tenant ON kol_info(tenant_id);
CREATE INDEX idx_email_logs_tenant ON email_logs(tenant_id);
CREATE INDEX idx_tags_tenant ON tags(tenant_id);
CREATE INDEX idx_email_templates_tenant ON email_templates(tenant_id);

-- 修改唯一约束，加入租户隔离
ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_project_code_key;
ALTER TABLE projects ADD CONSTRAINT projects_code_tenant_unique UNIQUE(project_code, tenant_id);

ALTER TABLE tags DROP CONSTRAINT IF EXISTS tags_name_key;
ALTER TABLE tags ADD CONSTRAINT tags_name_tenant_unique UNIQUE(name, tenant_id);

ALTER TABLE email_templates DROP CONSTRAINT IF EXISTS email_templates_template_code_key;
ALTER TABLE email_templates ADD CONSTRAINT email_templates_code_tenant_unique UNIQUE(template_code, tenant_id);
```

#### 行级安全策略 (RLS)
```sql
-- 启用行级安全
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE kol_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
CREATE POLICY tenant_isolation_projects ON projects
    FOR ALL
    TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant_id')::INTEGER);

CREATE POLICY tenant_isolation_kol_info ON kol_info
    FOR ALL
    TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant_id')::INTEGER);

CREATE POLICY tenant_isolation_email_logs ON email_logs
    FOR ALL
    TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant_id')::INTEGER);

CREATE POLICY tenant_isolation_tags ON tags
    FOR ALL
    TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant_id')::INTEGER);

CREATE POLICY tenant_isolation_email_templates ON email_templates
    FOR ALL
    TO authenticated_users
    USING (tenant_id = current_setting('app.current_tenant_id')::INTEGER);
```

### 2.3 权限控制矩阵

```sql
-- 插入基础权限
INSERT INTO permissions (permission_name, permission_code, resource, action, description) VALUES
('查看KOL', 'view_kol', 'kol', 'read', '查看KOL信息'),
('创建KOL', 'create_kol', 'kol', 'create', '创建新的KOL记录'),
('编辑KOL', 'edit_kol', 'kol', 'update', '编辑KOL信息'),
('删除KOL', 'delete_kol', 'kol', 'delete', '删除KOL记录'),
('导出KOL', 'export_kol', 'kol', 'export', '导出KOL数据'),

('查看项目', 'view_project', 'project', 'read', '查看项目信息'),
('创建项目', 'create_project', 'project', 'create', '创建新项目'),
('编辑项目', 'edit_project', 'project', 'update', '编辑项目信息'),
('删除项目', 'delete_project', 'project', 'delete', '删除项目'),

('查看邮件日志', 'view_email_log', 'email', 'read', '查看邮件发送记录'),
('发送邮件', 'send_email', 'email', 'create', '发送邮件给KOL'),
('编辑邮件模板', 'edit_email_template', 'email', 'update', '编辑邮件模板'),

('查看统计报表', 'view_analytics', 'analytics', 'read', '查看数据统计报表'),
('管理用户', 'manage_users', 'user', 'manage', '管理租户内用户'),
('管理角色', 'manage_roles', 'role', 'manage', '管理角色和权限'),
('系统设置', 'system_settings', 'system', 'manage', '系统配置管理');

-- 插入默认角色
INSERT INTO roles (role_name, role_code, description, is_system_role) VALUES
('系统管理员', 'admin', '拥有所有权限的管理员角色', true),
('项目经理', 'project_manager', '可以管理项目和KOL，发送邮件', true),
('运营专员', 'operator', '可以查看和编辑KOL，发送邮件', true),
('只读用户', 'viewer', '只能查看数据，不能编辑', true);
```

---

## 3. API层面的扩展支持

### 3.1 自定义字段API

#### 获取自定义字段定义
```http
GET /api/v1/custom-fields?category=kol
```

#### 设置自定义字段值
```http
PUT /api/v1/kols/{kol_id}/custom-fields
Content-Type: application/json

{
  "has_collaborated": true,
  "price_range": "$500-1000",
  "collaboration_interest": 8,
  "notes": "Very responsive, high quality content",
  "social_links": {
    "instagram": "https://instagram.com/johndoe",
    "twitter": "https://twitter.com/johndoe"
  }
}
```

#### 按自定义字段筛选
```http
GET /api/v1/projects/1/kols?custom_fields.has_collaborated=true&custom_fields.price_range=$500-1000
```

### 3.2 多租户API支持

#### 中间件实现
```python
# 伪代码示例
class TenantMiddleware:
    def process_request(self, request):
        # 从JWT token或请求头获取租户信息
        tenant_id = self.extract_tenant_id(request)
        
        # 设置数据库会话的租户上下文
        db.execute("SET app.current_tenant_id = %s", [tenant_id])
        
        # 将租户信息添加到请求上下文
        request.tenant_id = tenant_id
        request.tenant = get_tenant_by_id(tenant_id)
```

---

## 4. 前端扩展支持

### 4.1 动态表单生成

```javascript
// 示例：动态生成自定义字段表单
const CustomFieldForm = ({ entityType, entityId }) => {
  const [fields, setFields] = useState([]);
  const [values, setValues] = useState({});
  
  useEffect(() => {
    // 获取自定义字段定义
    fetchCustomFields(entityType).then(setFields);
    // 获取当前值
    fetchCustomFieldValues(entityType, entityId).then(setValues);
  }, [entityType, entityId]);
  
  const renderField = (field) => {
    switch (field.field_type) {
      case 'text':
        return <Input {...field} value={values[field.field_code]} />;
      case 'boolean':
        return <Checkbox {...field} checked={values[field.field_code]} />;
      case 'select':
        return <Select {...field} options={field.field_config.options} />;
      case 'date':
        return <DatePicker {...field} value={values[field.field_code]} />;
      default:
        return null;
    }
  };
  
  return (
    <Form>
      {fields.map(field => (
        <FormItem key={field.field_code} label={field.display_name}>
          {renderField(field)}
        </FormItem>
      ))}
    </Form>
  );
};
```

### 4.2 权限控制组件

```javascript
// 权限控制高阶组件
const withPermission = (permission) => (Component) => {
  return (props) => {
    const { user } = useAuth();
    
    if (!hasPermission(user, permission)) {
      return <div>无权限访问</div>;
    }
    
    return <Component {...props} />;
  };
};

// 使用示例
const KOLCreateButton = withPermission('create_kol')(Button);
const KOLDeleteButton = withPermission('delete_kol')(Button);
```

---

## 5. 性能优化策略

### 5.1 缓存策略
```sql
-- 创建物化视图缓存常用查询
CREATE MATERIALIZED VIEW mv_kol_summary AS
SELECT 
    k.tenant_id,
    k.project_id,
    k.platform,
    k.country,
    COUNT(*) as total_kols,
    AVG(k.followers_count) as avg_followers,
    AVG(k.engagement_rate) as avg_engagement,
    COUNT(CASE WHEN k.email IS NOT NULL THEN 1 END) as kols_with_email
FROM kol_info k
GROUP BY k.tenant_id, k.project_id, k.platform, k.country;

-- 创建定时刷新任务
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW mv_kol_summary;
END;
$$ LANGUAGE plpgsql;
```

### 5.2 分区策略
```sql
-- 按租户ID对大表进行分区
CREATE TABLE kol_info_partitioned (
    LIKE kol_info INCLUDING ALL
) PARTITION BY HASH (tenant_id);

-- 创建分区表
CREATE TABLE kol_info_partition_0 PARTITION OF kol_info_partitioned
    FOR VALUES WITH (MODULUS 4, REMAINDER 0);
CREATE TABLE kol_info_partition_1 PARTITION OF kol_info_partitioned
    FOR VALUES WITH (MODULUS 4, REMAINDER 1);
CREATE TABLE kol_info_partition_2 PARTITION OF kol_info_partitioned
    FOR VALUES WITH (MODULUS 4, REMAINDER 2);
CREATE TABLE kol_info_partition_3 PARTITION OF kol_info_partitioned
    FOR VALUES WITH (MODULUS 4, REMAINDER 3);
```

---

## 6. 监控和审计

### 6.1 审计日志表
```sql
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id),
    user_id INTEGER REFERENCES users(id),
    
    -- 操作信息
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id VARCHAR(255),
    
    -- 变更详情
    old_values JSONB,
    new_values JSONB,
    
    -- 请求信息
    ip_address INET,
    user_agent TEXT,
    request_id VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建审计触发器
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_logs (
        tenant_id, user_id, action, resource_type, resource_id,
        old_values, new_values, ip_address, user_agent
    ) VALUES (
        COALESCE(NEW.tenant_id, OLD.tenant_id),
        current_setting('app.current_user_id')::INTEGER,
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id)::TEXT,
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN row_to_json(NEW) ELSE NULL END,
        current_setting('app.client_ip')::INET,
        current_setting('app.user_agent')
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;
```

---

## 7. 部署和运维考虑

### 7.1 配置管理
- 使用环境变量管理不同租户的配置
- 支持热更新配置，无需重启服务
- 配置版本控制和回滚机制

### 7.2 监控指标
- 按租户统计的API调用量
- 数据库查询性能监控
- 自定义字段使用情况统计
- 用户行为分析

### 7.3 备份策略
- 按租户进行数据备份
- 支持单租户数据恢复
- 定期数据一致性检查

---

## 8. 最佳实践建议

### 8.1 开发规范
1. **统一的租户上下文传递**：所有API都必须包含租户信息
2. **自定义字段验证**：严格的字段类型和值验证
3. **权限检查**：每个操作都要进行权限验证
4. **数据隔离测试**：确保不同租户数据完全隔离

### 8.2 安全考虑
1. **SQL注入防护**：使用参数化查询
2. **跨租户访问防护**：严格的租户隔离检查
3. **敏感数据加密**：自定义字段中的敏感信息加密存储
4. **审计日志保护**：审计日志不可篡改

### 8.3 性能优化
1. **索引优化**：为自定义字段查询创建合适的索引
2. **查询优化**：避免跨租户的复杂查询
3. **缓存策略**：合理使用Redis缓存热点数据
4. **分页处理**：大数据量查询必须分页

这个可扩展性设计方案既满足了自定义字段的灵活性需求，又实现了多租户的数据隔离和权限控制，为系统的长期发展奠定了坚实的基础。 