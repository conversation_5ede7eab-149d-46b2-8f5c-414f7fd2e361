-- KOL管理平台数据迁移脚本
-- 从老表结构迁移到新表结构
-- 执行前请备份数据库

-- 1. 创建临时表存储老数据
CREATE TABLE IF NOT EXISTS old_kol_info_backup AS SELECT * FROM kol_info;

-- 2. 迁移hashtag数据到新的kol_hashtags表
INSERT INTO kol_hashtags (kol_id, hashtag, frequency, source, created_at)
SELECT 
    kol_id,
    TRIM(BOTH '"' FROM hashtag_value) as hashtag,
    1 as frequency,
    'migrated' as source,
    created_at
FROM (
    SELECT 
        kol_id,
        jsonb_array_elements_text(
            CASE 
                WHEN most_used_hashtags = 'null' OR most_used_hashtags IS NULL THEN '[]'::jsonb
                ELSE most_used_hashtags::jsonb
            END
        ) as hashtag_value,
        created_at
    FROM old_kol_info_backup
    WHERE most_used_hashtags IS NOT NULL 
    AND most_used_hashtags != 'null'
    AND most_used_hashtags != ''
) hashtag_extraction
WHERE hashtag_value IS NOT NULL 
AND hashtag_value != ''
ON CONFLICT (kol_id, hashtag) DO NOTHING;

-- 3. 创建默认筛选条件（用于无法追溯的老数据）
INSERT INTO filter_conditions (
    project_id, 
    filter_name, 
    source_platform, 
    target_platform,
    created_by,
    total_results,
    imported_count
) VALUES (
    1, -- 默认项目ID
    'Legacy-Data-Migration',
    'legacy',
    'mixed',
    'migration_script',
    (SELECT COUNT(*) FROM old_kol_info_backup),
    (SELECT COUNT(*) FROM old_kol_info_backup)
) ON CONFLICT DO NOTHING;

-- 4. 为所有老数据创建筛选条件关联
INSERT INTO kol_filter_associations (
    kol_id,
    filter_condition_id,
    import_batch_id,
    matched_criteria,
    created_at
)
SELECT 
    kol_id,
    (SELECT id FROM filter_conditions WHERE filter_name = 'Legacy-Data-Migration'),
    'migration-batch-' || TO_CHAR(NOW(), 'YYYYMMDD'),
    jsonb_build_object(
        'original_source', source,
        'original_level', level,
        'migration_note', 'Migrated from old system'
    ),
    created_at
FROM old_kol_info_backup
ON CONFLICT (kol_id, filter_condition_id) DO NOTHING;

-- 5. 根据老数据的level字段设置tier
UPDATE kol_info SET tier = 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM old_kol_info_backup o 
            WHERE o.kol_id = kol_info.kol_id 
            AND (o.level ILIKE '%nano%' OR o.level ILIKE '%<1k%')
        ) THEN 'nano'
        WHEN EXISTS (
            SELECT 1 FROM old_kol_info_backup o 
            WHERE o.kol_id = kol_info.kol_id 
            AND (o.level ILIKE '%micro%' OR o.level ILIKE '%1k%' OR o.level ILIKE '%10k%')
        ) THEN 'micro'
        WHEN EXISTS (
            SELECT 1 FROM old_kol_info_backup o 
            WHERE o.kol_id = kol_info.kol_id 
            AND (o.level ILIKE '%mid%' OR o.level ILIKE '%50k%' OR o.level ILIKE '%100k%')
        ) THEN 'mid'
        WHEN EXISTS (
            SELECT 1 FROM old_kol_info_backup o 
            WHERE o.kol_id = kol_info.kol_id 
            AND (o.level ILIKE '%macro%' OR o.level ILIKE '%500k%' OR o.level ILIKE '%1m%')
        ) THEN 'macro'
        WHEN EXISTS (
            SELECT 1 FROM old_kol_info_backup o 
            WHERE o.kol_id = kol_info.kol_id 
            AND (o.level ILIKE '%mega%' OR o.level ILIKE '%>1m%')
        ) THEN 'mega'
        ELSE NULL
    END
WHERE tier IS NULL;

-- 6. 迁移likes_count字段（从老数据的likes_k字段）
UPDATE kol_info SET likes_count = 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM old_kol_info_backup o 
            WHERE o.kol_id = kol_info.kol_id 
            AND o.likes_k IS NOT NULL
        ) THEN (
            SELECT CAST(o.likes_k * 1000 AS BIGINT) 
            FROM old_kol_info_backup o 
            WHERE o.kol_id = kol_info.kol_id
        )
        ELSE NULL
    END
WHERE likes_count IS NULL;

-- 7. 根据hashtag自动添加内容标签
INSERT INTO kol_tags (kol_id, tag_id, project_id, confidence_score, source, created_at)
SELECT DISTINCT
    h.kol_id,
    t.id as tag_id,
    k.project_id,
    0.8 as confidence_score,
    'hashtag_extracted' as source,
    NOW()
FROM kol_hashtags h
JOIN kol_info k ON h.kol_id = k.kol_id
JOIN tags t ON (
    (h.hashtag ILIKE '%gaming%' OR h.hashtag ILIKE '%game%' OR h.hashtag ILIKE '%gamer%') AND t.name = 'Gaming'
    OR (h.hashtag ILIKE '%fashion%' OR h.hashtag ILIKE '%style%' OR h.hashtag ILIKE '%outfit%') AND t.name = 'Fashion'
    OR (h.hashtag ILIKE '%beauty%' OR h.hashtag ILIKE '%makeup%' OR h.hashtag ILIKE '%cosmetic%') AND t.name = 'Beauty'
    OR (h.hashtag ILIKE '%food%' OR h.hashtag ILIKE '%cooking%' OR h.hashtag ILIKE '%recipe%') AND t.name = 'Food'
    OR (h.hashtag ILIKE '%travel%' OR h.hashtag ILIKE '%trip%' OR h.hashtag ILIKE '%vacation%') AND t.name = 'Travel'
    OR (h.hashtag ILIKE '%tech%' OR h.hashtag ILIKE '%technology%' OR h.hashtag ILIKE '%gadget%') AND t.name = 'Tech'
    OR (h.hashtag ILIKE '%fitness%' OR h.hashtag ILIKE '%workout%' OR h.hashtag ILIKE '%gym%') AND t.name = 'Fitness'
    OR (h.hashtag ILIKE '%music%' OR h.hashtag ILIKE '%song%' OR h.hashtag ILIKE '%musician%') AND t.name = 'Music'
    OR (h.hashtag ILIKE '%dance%' OR h.hashtag ILIKE '%dancing%' OR h.hashtag ILIKE '%dancer%') AND t.name = 'Dance'
    OR (h.hashtag ILIKE '%comedy%' OR h.hashtag ILIKE '%funny%' OR h.hashtag ILIKE '%humor%') AND t.name = 'Comedy'
    OR (h.hashtag ILIKE '%pet%' OR h.hashtag ILIKE '%dog%' OR h.hashtag ILIKE '%cat%') AND t.name = 'Pets'
    OR (h.hashtag ILIKE '%parent%' OR h.hashtag ILIKE '%mom%' OR h.hashtag ILIKE '%dad%' OR h.hashtag ILIKE '%baby%') AND t.name = 'Parenting'
)
ON CONFLICT (kol_id, tag_id, project_id) DO NOTHING;

-- 8. 根据粉丝数自动添加性能标签
INSERT INTO kol_tags (kol_id, tag_id, project_id, confidence_score, source, created_at)
SELECT 
    k.kol_id,
    t.id as tag_id,
    k.project_id,
    0.9 as confidence_score,
    'auto' as source,
    NOW()
FROM kol_info k
JOIN tags t ON (
    (k.engagement_rate > 0.05 AND t.name = 'High-Engagement')
    OR (k.engagement_rate BETWEEN 0.02 AND 0.05 AND t.name = 'Medium-Engagement')
)
WHERE k.engagement_rate IS NOT NULL
ON CONFLICT (kol_id, tag_id, project_id) DO NOTHING;

-- 9. 数据质量检查和修复
-- 修复空的email字段（将'NaN'替换为NULL）
UPDATE kol_info SET email = NULL WHERE email = 'NaN' OR email = 'null' OR email = '';

-- 修复空的username字段
UPDATE kol_info SET username = kol_name WHERE username IS NULL OR username = '';

-- 修复followers_count为负数的情况
UPDATE kol_info SET followers_count = 0 WHERE followers_count < 0;

-- 修复engagement_rate超出合理范围的情况
UPDATE kol_info SET engagement_rate = NULL WHERE engagement_rate > 1 OR engagement_rate < 0;

-- 10. 创建迁移报告
CREATE OR REPLACE VIEW migration_report AS
SELECT 
    'Total KOLs in old system' as metric,
    COUNT(*)::TEXT as value
FROM old_kol_info_backup
UNION ALL
SELECT 
    'Total KOLs migrated' as metric,
    COUNT(*)::TEXT as value
FROM kol_info
UNION ALL
SELECT 
    'Total hashtags extracted' as metric,
    COUNT(*)::TEXT as value
FROM kol_hashtags
UNION ALL
SELECT 
    'KOLs with email' as metric,
    COUNT(*)::TEXT as value
FROM kol_info
WHERE email IS NOT NULL
UNION ALL
SELECT 
    'KOLs with tier assigned' as metric,
    COUNT(*)::TEXT as value
FROM kol_info
WHERE tier IS NOT NULL
UNION ALL
SELECT 
    'Auto-generated tags' as metric,
    COUNT(*)::TEXT as value
FROM kol_tags
WHERE source IN ('hashtag_extracted', 'auto');

-- 显示迁移报告
SELECT * FROM migration_report;

-- 11. 清理临时数据（可选，建议保留一段时间）
-- DROP TABLE IF EXISTS old_kol_info_backup;

-- 迁移完成提示
DO $$
BEGIN
    RAISE NOTICE '=== 数据迁移完成 ===';
    RAISE NOTICE '请查看migration_report视图了解迁移统计信息';
    RAISE NOTICE '建议保留old_kol_info_backup表一段时间以备回滚';
END $$; 