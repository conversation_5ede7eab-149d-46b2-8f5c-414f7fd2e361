CREATE TABLE public.kol_info (
	kol_id varchar NOT NULL,
	kol_name varchar NOT NULL,
	username varchar NULL,
	email varchar NULL,
	bio varchar NULL,
	account_link varchar NULL,
	followers_k float8 NULL,
	likes_k float8 NULL,
	platform varchar NULL,
	"source" varchar NULL,
	slug varchar NULL,
	creator_id varchar NULL,
	mean_views_k float8 NULL,
	median_views_k float8 NULL,
	engagement_rate float8 NULL,
	average_views_k float8 NULL,
	average_likes_k float8 NULL,
	average_comments_k float8 NULL,
	most_used_hashtags jsonb NULL,
	"level" varchar NULL,
	keywords_ai jsonb NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	CONSTRAINT kol_info_pkey PRIMARY KEY (kol_id)
);
CREATE INDEX ix_kol_info_average_comments_k ON public.kol_info USING btree (average_comments_k);
CREATE INDEX ix_kol_info_average_likes_k ON public.kol_info USING btree (average_likes_k);
CREATE INDEX ix_kol_info_average_views_k ON public.kol_info USING btree (average_views_k);
CREATE INDEX ix_kol_info_created_at ON public.kol_info USING btree (created_at);
CREATE INDEX ix_kol_info_creator_id ON public.kol_info USING btree (creator_id);
CREATE INDEX ix_kol_info_email ON public.kol_info USING btree (email);
CREATE INDEX ix_kol_info_engagement_rate ON public.kol_info USING btree (engagement_rate);
CREATE INDEX ix_kol_info_followers_k ON public.kol_info USING btree (followers_k);
CREATE INDEX ix_kol_info_keywords_ai ON public.kol_info USING gin (keywords_ai);
CREATE INDEX ix_kol_info_kol_id ON public.kol_info USING btree (kol_id);
CREATE INDEX ix_kol_info_kol_name ON public.kol_info USING btree (kol_name);
CREATE INDEX ix_kol_info_level ON public.kol_info USING btree (level);
CREATE INDEX ix_kol_info_likes_k ON public.kol_info USING btree (likes_k);
CREATE INDEX ix_kol_info_mean_views_k ON public.kol_info USING btree (mean_views_k);
CREATE INDEX ix_kol_info_median_views_k ON public.kol_info USING btree (median_views_k);
CREATE INDEX ix_kol_info_most_used_hashtags ON public.kol_info USING gin (most_used_hashtags);
CREATE INDEX ix_kol_info_platform ON public.kol_info USING btree (platform);
CREATE INDEX ix_kol_info_slug ON public.kol_info USING btree (slug);
CREATE INDEX ix_kol_info_source ON public.kol_info USING btree (source);
CREATE INDEX ix_kol_info_updated_at ON public.kol_info USING btree (updated_at);
CREATE INDEX ix_kol_info_username ON public.kol_info USING btree (username);




