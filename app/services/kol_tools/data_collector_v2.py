#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project ：KOL-python
@Date    ：2025/4/9 下午4:39
@Description: KOL数据采集工具V2版本，支持QPS控制和线程安全统计
'''
import json
import os
import hashlib
from typing import Dict, List, Optional
import argparse
import requests

from app.services.llm_service import email_and_keywords_from_bio
from app.services.tools import fte_compute, compute_view_mean_and_median_k
from app.logging_config import get_task_logger
from app.core.config import settings
import concurrent.futures
import threading
from functools import partial
import datetime
import time

# 从 tools.py 导入必要的函数
from app.services.kol_tools.tools import (
    _validate_params,
    _collect_source_data,
    _standardize_data,
    _collect_platform_data,
    _parse_table_url
)

from app.services.feishu_lark_service import Lark

# 使用任务日志记录器
logger = get_task_logger("app.worker.tasks.kol_data")
# 设置环境变量：'test' 或 'prod'
env = 'prod'

# 设置 base_url
if env == 'test':
    base_url = 'http://172.20.11.70:8000'
else:
    base_url = 'http://54.84.111.234:8000'  # 正式环境地址

# 缓存目录
CACHE_DIR = "cache/raw_data"


def get_cache_filename(filter_name: str, source_name: str, platform_name: str) -> str:
    """
    生成缓存文件名
    
    Args:
        filter_name: 过滤条件名称
        source_name: 数据源名称
        platform_name: 平台名称
        
    Returns:
        str: 缓存文件名
    """
    # 使用filter_name、source_name、platform_name生成唯一标识
    cache_key = f"{filter_name}_{source_name}_{platform_name}"
    # 使用MD5生成固定长度的文件名
    filename = hashlib.md5(cache_key.encode()).hexdigest() + ".json"
    return filename


def save_raw_data_to_cache(raw_data_list: List[Dict], filter_name: str, source_name: str, platform_name: str) -> str:
    """
    将原始数据保存到缓存文件
    
    Args:
        raw_data_list: 原始数据列表
        filter_name: 过滤条件名称
        source_name: 数据源名称
        platform_name: 平台名称
        
    Returns:
        str: 缓存文件路径
    """
    # 确保缓存目录存在
    os.makedirs(CACHE_DIR, exist_ok=True)
    
    # 生成缓存文件名
    filename = get_cache_filename(filter_name, source_name, platform_name)
    cache_path = os.path.join(CACHE_DIR, filename)
    
    # 保存数据到文件
    cache_data = {
        "filter_name": filter_name,
        "source_name": source_name,
        "platform_name": platform_name,
        "timestamp": datetime.datetime.now().isoformat(),
        "data_count": len(raw_data_list),
        "raw_data": raw_data_list
    }
    
    with open(cache_path, 'w', encoding='utf-8') as f:
        json.dump(cache_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"💾 原始数据已缓存到: {cache_path} (共 {len(raw_data_list)} 条记录)")
    return cache_path


def load_raw_data_from_cache(filter_name: str, source_name: str, platform_name: str) -> Optional[List[Dict]]:
    """
    从缓存文件加载原始数据
    
    Args:
        filter_name: 过滤条件名称
        source_name: 数据源名称
        platform_name: 平台名称
        
    Returns:
        Optional[List[Dict]]: 原始数据列表，如果缓存不存在则返回None
    """
    filename = get_cache_filename(filter_name, source_name, platform_name)
    cache_path = os.path.join(CACHE_DIR, filename)
    
    if not os.path.exists(cache_path):
        logger.info(f"📂 缓存文件不存在: {cache_path}")
        return None
    
    try:
        with open(cache_path, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)
        
        # 验证缓存数据的有效性
        if (cache_data.get("filter_name") == filter_name and 
            cache_data.get("source_name") == source_name and 
            cache_data.get("platform_name") == platform_name):
            
            raw_data_list = cache_data.get("raw_data", [])
            logger.info(f"📂 从缓存加载原始数据: {cache_path} (共 {len(raw_data_list)} 条记录)")
            return raw_data_list
        else:
            logger.warning(f"⚠️ 缓存文件数据不匹配，忽略缓存: {cache_path}")
            return None
            
    except Exception as e:
        logger.error(f"❌ 读取缓存文件失败: {cache_path}, 错误: {str(e)}")
        return None


def delete_cache_file(filter_name: str, source_name: str, platform_name: str) -> bool:
    """
    删除缓存文件
    
    Args:
        filter_name: 过滤条件名称
        source_name: 数据源名称
        platform_name: 平台名称
        
    Returns:
        bool: 是否成功删除
    """
    filename = get_cache_filename(filter_name, source_name, platform_name)
    cache_path = os.path.join(CACHE_DIR, filename)
    
    try:
        if os.path.exists(cache_path):
            os.remove(cache_path)
            logger.info(f"🗑️ 缓存文件已删除: {cache_path}")
            return True
        else:
            logger.info(f"📂 缓存文件不存在，无需删除: {cache_path}")
            return True
    except Exception as e:
        logger.error(f"❌ 删除缓存文件失败: {cache_path}, 错误: {str(e)}")
        return False


def ensure_lark_token_valid(lark_client: Lark) -> None:
    """
    确保Lark客户端的tenant token有效，如果无效或过期则刷新

    Args:
        lark_client: Lark客户端实例
    """
    try:
        # 检查token是否存在且未过期
        if not lark_client._tenant_token or (
                lark_client._token_expire_time and
                lark_client._token_expire_time < datetime.datetime.now()
        ):
            logger.info("🔑 Lark tenant token已过期或不存在，正在刷新...")
            # 通过调用API强制刷新token
            # 这里假设获取wiki信息就会刷新token
            test_result = lark_client.get_wiki_node_info(node_token="QTRqwY8vFiplVUkP5CMc9e9Fn1f")
            # 无论成功失败，token都应该被刷新了
            logger.info("✅ Lark tenant token已刷新完成")
        else:
            logger.debug("🔑 Lark tenant token状态正常，无需刷新")
    except Exception as e:
        logger.warning(f"⚠️ 刷新Lark token时出现异常: {str(e)}")
        # 这里不抛出异常，让后续操作自行处理token问题
        pass


def collect_kol_data_v2(query_params: Dict, lark_client: Lark, record_id: str) -> Dict:
    """
    优化后的KOL数据采集主函数，支持QPS控制和线程安全统计。

    Args:
        query_params: 查询参数字典，包含数据源、平台、过滤条件等信息
        lark_client: Lark客户端实例，用于更新任务状态
        record_id: 飞书表格记录ID，用于更新任务状态

    Returns:
        Dict: 包含任务执行结果的字典
    """
    task_start_time = time.time()
    logger.info("🚀 ========== 开始KOL数据采集任务 ==========")
    logger.info(
        f"📋 任务参数: 平台={query_params.get('platform')}, 数据源={query_params.get('source')}, 过滤条件={query_params.get('filter_name')}")

    try:
        # 参数提取和验证
        source_name = query_params.get("source", "").lower()
        platform_name = query_params.get("platform", "").lower()
        filter_name = query_params.get("filter_name", "")
        project_code = query_params.get("project_code", "")

        # 线程池配置
        max_worker_threads = settings.MAX_WORKERS if hasattr(settings, 'MAX_WORKERS') else 8
        logger.info(f"⚙️ 系统配置: 最大工作线程数={max_worker_threads}, QPS限制=10/s")

        # 参数验证
        _validate_params(source_name, platform_name)
        logger.info("✅ 参数验证通过")

        # 确保Lark token有效
        ensure_lark_token_valid(lark_client)

        logger.info("📢 任务开始执行")

        # 初始化线程安全统计字典
        task_statistics = {
            "processed_count": 0,  # 已处理数量
            "success_count": 0,  # 成功数量
            "failed_count": 0,  # 失败数量
            "skipped_count": 0,  # 跳过数量
            "email_count": 0,  # 有效邮箱数量
            "lock": threading.Lock(),  # 线程锁
            "high_potential_records": [],  # 高潜力KOL记录
            "duplicate_records": []  # 重复记录
        }

        try:
            # 第一阶段：采集原始数据
            logger.info("📊 ========== 第一阶段：数据源采集 ==========")
            logger.info(f"🔍 正在从数据源 '{source_name}' 采集原始数据...")
            
            # 首先尝试从缓存加载数据
            raw_data_list = load_raw_data_from_cache(filter_name, source_name, platform_name)
            
            if raw_data_list is None:
                # 缓存不存在，从数据源采集
                logger.info(f"📡 缓存不存在，开始从数据源 '{source_name}' 采集原始数据...")
                raw_data_list = _collect_source_data(source_name, query_params)
                
                if raw_data_list:
                    # 采集成功，保存到缓存
                    save_raw_data_to_cache(raw_data_list, filter_name, source_name, platform_name)
                else:
                    error_msg = f"❌ 从数据源 '{source_name}' 获取数据失败"
                    logger.error(error_msg)
                    return {"status": "error", "message": error_msg}
            else:
                logger.info(f"✅ 使用缓存数据，跳过数据源采集")

            logger.info(f"✅ 原始数据采集完成，共获取 {len(raw_data_list)} 条记录")

            # 第二阶段：数据标准化处理
            logger.info("🔄 ========== 第二阶段：数据标准化 ==========")
            logger.info(f"🛠️ 正在对 {len(raw_data_list)} 条原始数据进行标准化处理...")
            standardized_data_list = _standardize_data(raw_data_list, source_name, platform_name)

            if not standardized_data_list:
                error_msg = "❌ 数据标准化处理失败"
                logger.error(error_msg)
                return {"status": "error", "message": error_msg}

            logger.info(f"✅ 数据标准化完成，共处理 {len(standardized_data_list)} 条记录")

            # 第三阶段：多线程处理KOL数据
            logger.info("⚡ ========== 第三阶段：多线程数据处理 ==========")
            total_records_count = len(standardized_data_list)
            logger.info(f"🎯 开始多线程处理，总记录数: {total_records_count}, 工作线程数: {max_worker_threads}")

            # 计算FTE（Full Time Equivalent）
            fte_compute('UA_KOL', 'UA_KOL_Standard_Database_TK', total_records_count)
            logger.info("📈 FTE计算完成")

            # 过滤已存在的KOL数据
            logger.info("🔍 正在检查数据库中已存在的KOL记录...")
            existing_kol_data = get_existing_kol_data(standardized_data_list, project_code, platform_name)
            logger.info(f"📋 发现已存在的KOL记录: {len(existing_kol_data)} 条")

            # 所有KOL都需要处理，包括已存在的
            all_kol_data_list = standardized_data_list
            logger.info(f"📊 需要处理的所有KOL记录: {len(all_kol_data_list)} 条")

            # 创建QPS控制器实例
            qps_limiter = QPSController(8)  # 限制每秒10次请求
            logger.info("⏱️ QPS控制器已初始化，限制: 10次/秒")

            # 执行多线程处理
            logger.info("🚀 开始多线程并发处理...")
            logger.info(f"📊 准备处理 {len(all_kol_data_list)} 条KOL数据，使用 {max_worker_threads} 个线程")

            if len(all_kol_data_list) == 0:
                logger.info("ℹ️ 没有KOL数据需要处理，跳过多线程处理阶段")
            else:
                logger.info(f"✅ 确认开始处理 {len(all_kol_data_list)} 条KOL数据")

                with concurrent.futures.ThreadPoolExecutor(max_workers=max_worker_threads) as thread_pool:
                    # 创建处理函数，绑定固定参数
                    process_function = partial(
                        process_single_kol,
                        platform_name=platform_name,
                        total_count=len(all_kol_data_list),
                        stats=task_statistics,
                        query_params=query_params,
                        qps_controller=qps_limiter,
                        lark_client=lark_client,
                        existing_kol_data=existing_kol_data
                    )

                    # 提交所有任务到线程池
                    logger.info(f"📤 正在提交 {len(all_kol_data_list)} 个任务到线程池...")
                    future_tasks = [
                        thread_pool.submit(process_function, kol_item)
                        for kol_item in all_kol_data_list
                    ]
                    logger.info(f"✅ 已提交 {len(future_tasks)} 个任务到线程池，开始执行...")

                    # 等待所有任务完成，并添加超时和进度检查
                    try:
                        logger.info("⏳ 等待所有线程任务完成...")
                        completed_futures = concurrent.futures.wait(future_tasks, timeout=3600)  # 1小时超时
                        logger.info(f"✅ 线程池任务执行完成，完成数量：{len(completed_futures.done)}")

                        # 检查是否有任务超时
                        if completed_futures.not_done:
                            logger.warning(f"⚠️ 有 {len(completed_futures.not_done)} 个任务超时未完成")

                        # 检查任务执行结果
                        failed_tasks = 0
                        for future in completed_futures.done:
                            try:
                                future.result()  # 获取结果，如果有异常会抛出
                            except Exception as e:
                                failed_tasks += 1
                                logger.error(f"❌ 任务执行失败: {str(e)}")

                        if failed_tasks > 0:
                            logger.warning(f"⚠️ 有 {failed_tasks} 个任务执行失败")

                    except concurrent.futures.TimeoutError:
                        logger.error("❌ 多线程任务执行超时")
                    except Exception as e:
                        logger.error(f"❌ 多线程执行过程中发生异常: {str(e)}")

                logger.info("🔚 多线程处理阶段完成")

            # 等待日志输出完成
            time.sleep(0.5)

            # 第四阶段：任务完成总结
            logger.info("🎉 ========== 第四阶段：任务完成 ==========")
            logger.info(f"✅ 平台 '{platform_name}' 的KOL数据采集任务完成")
            logger.info(f"📊 处理统计: 总计 {total_records_count}/{total_records_count} (100%)")
            logger.info("=" * 60)

            # 输出详细统计信息
            logger.info(f"📈 详细统计信息:")
            logger.info(f"   • 总记录数: {total_records_count}")
            logger.info(f"   • 成功处理: {task_statistics['success_count']}")
            logger.info(f"   • 处理失败: {task_statistics['failed_count']}")
            logger.info(f"   • 跳过记录: {task_statistics['skipped_count']}")
            logger.info(f"   • 有效邮箱: {task_statistics['email_count']}")

            # 处理重复数据统计
            duplicate_records_count = len(task_statistics['duplicate_records'])
            logger.info(f"🔄 重复数据统计: {duplicate_records_count} 条")

            if duplicate_records_count > 0:
                sample_duplicate = task_statistics['duplicate_records'][0]
                logger.info(f"📝 重复数据示例: {sample_duplicate.get('kol_name', '未知')}")

            # 任务数据处理完成，删除缓存文件
            logger.info("🗑️ 数据处理完成，删除缓存文件...")
            delete_cache_file(filter_name, source_name, platform_name)

            # 发送最终完成通知
            completion_message = (
                f"总采集数:{total_records_count}条，"
                f"成功:{task_statistics['success_count']}条，"
                f"失败:{task_statistics['failed_count']}条，"
                f"跳过:{task_statistics['skipped_count']}条，"
                f"重复:{duplicate_records_count}条"
            )

            # 准备重复数据的简化版本
            simplified_duplicates_list = []
            for duplicate_record in task_statistics['duplicate_records']:
                simplified_record = {
                    "kol_name": duplicate_record.get("kol_name", ""),
                    "account_link": duplicate_record.get("source_data", {}).get("account_link", ""),
                    "username": duplicate_record.get("source_data", {}).get("username", ""),
                    "timestamp": duplicate_record.get("timestamp", "")
                }
                simplified_duplicates_list.append(simplified_record)

            # 更新飞书表格记录
            record_update_data = {
                "点击": True,
                "Status": "已完成",
                "标记": "本地",
                "Remark": f"成功采集数据 {task_statistics['success_count']} 条，失败 {task_statistics['failed_count']} 条，跳过 {task_statistics['skipped_count']} 条，重复 {duplicate_records_count} 条",
                "Unique Count": int(task_statistics['success_count']),
                "Valid Email": int(task_statistics['email_count']),
                "Date": None
            }

            update_response = lark_client.update_table_record(
                "PU8Qb8atYaXdylsxolOcSHrgn4e",
                "tbl52mnkqPC24Kya",
                record_id,
                record_update_data
            )
            logger.info(f"📝 飞书表格记录更新完成: {update_response}")

            # 计算任务执行时间
            task_duration = time.time() - task_start_time
            logger.info(f"⏱️ 任务总耗时: {task_duration:.2f} 秒")

            # 返回成功结果
            return {
                "status": "success",
                "message": completion_message,
                "data_count": total_records_count,
                "success_count": task_statistics['success_count'],
                "failed_count": task_statistics['failed_count'],
                "skipped_count": task_statistics['skipped_count'],
                "unique_count": len(task_statistics['high_potential_records']),
                "email_count": task_statistics['email_count'],
                "duplicate_count": duplicate_records_count,
                "duplicate_records": simplified_duplicates_list,
                "execution_time": task_duration
            }

        except Exception as e:
            error_msg = f"❌ 采集KOL数据时发生错误: {str(e)}"
            logger.exception(error_msg)

            # 更新失败状态
            failure_record_data = {
                "fields": {
                    "Status": "失败",
                }
            }
            lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id,
                                            failure_record_data)
            return {"status": "error", "message": str(e)}

    except Exception as e:
        error_msg = f"❌ 执行采集KOL数据时发生严重错误: {str(e)}"
        logger.exception(error_msg)

        # 更新失败状态
        failure_record_data = {
            "fields": {
                "Status": "失败",
            }
        }
        lark_client.update_table_record("PU8Qb8atYaXdylsxolOcSHrgn4e", "tbl52mnkqPC24Kya", record_id,
                                        failure_record_data)
        return {"status": "error", "message": str(e)}


def get_existing_kol_data(standardized_data_list: List[Dict], project_code: str, platform_name: str) -> Dict[str, Dict]:
    """
    查询数据库中已存在的KOL数据，用于去重处理。

    Args:
        standardized_data_list: 标准化后的KOL数据列表
        project_code: 项目代码

    Returns:
        Dict[str, Dict]: 已存在的KOL数据字典，key为kol_id，value为完整的KOL数据
    """
    logger.info(f"🔍 开始查询项目 '{project_code}' 中已存在的KOL记录...")

    api_url = f"{base_url}/api/v1/kol/advanced-search"
    search_conditions = [
        {
            "field": "kol_id",
            "operator": "eq",
            "value": kol_item["kol_id"]
        } for kol_item in standardized_data_list
    ]

    existing_kol_data_dict = {}
    current_page = 1
    page_size_limit = 100
    total_queries = 0

    while True:
        total_queries += 1
        logger.debug(f"📄 正在查询第 {current_page} 页数据...")

        try:
            response = requests.post(
                url=api_url,
                json={
                    "conditions": search_conditions,
                    "platform": platform_name,
                    # "project_code": project_code,
                    "skip": (current_page - 1) * page_size_limit,
                    "limit": page_size_limit,
                    "conjunction": "or"
                },
                timeout=30  # 设置30秒超时
            )
            response.raise_for_status()
            response_data = response.json()
            items_list = response_data.get("items", [])

            if not items_list:
                logger.debug(f"📄 第 {current_page} 页无数据，查询结束")
                break

            # 收集已存在的KOL数据
            for item in items_list:
                existing_kol_data_dict[item["kol_id"]] = item

            logger.debug(f"📄 第 {current_page} 页查询完成，当前已收集 {len(existing_kol_data_dict)} 个重复记录")

            # 检查是否已获取所有数据
            if len(existing_kol_data_dict) >= response_data.get("total", 0):
                logger.debug("📄 已获取所有重复数据，查询结束")
                break

            current_page += 1

        except requests.exceptions.Timeout:
            logger.warning(f"⏰ 第 {current_page} 页查询超时，继续下一页")
            current_page += 1
            continue
        except Exception as e:
            logger.error(f"❌ 查询第 {current_page} 页时发生错误: {str(e)}")
            break

    logger.info(f"✅ 重复KOL查询完成，共查询 {total_queries} 次，发现 {len(existing_kol_data_dict)} 个重复记录")

    return existing_kol_data_dict


def collect_platform_data_single(platform_name: str, kol_item: Dict, query_params: Dict) -> Optional[Dict]:
    """
    采集单个KOL的平台详细数据

    Args:
        platform_name: 平台名称（TikTok/Instagram/YouTube）
        kol_item: 单个KOL的标准化数据
        query_params: 查询参数

    Returns:
        Optional[Dict]: 采集到的平台详细数据，失败则返回None
    """
    kol_name = kol_item.get('kol_name', '未知KOL')
    kol_id = kol_item.get('kol_id', '未知ID')

    logger.info(f"🔍 开始采集平台 '{platform_name}' 的KOL详细数据: {kol_name} (ID: {kol_id})")

    try:
        # 构建单条数据列表，利用现有的批量采集函数
        single_kol_list = [kol_item]
        platform_data_result = _collect_platform_data(platform_name, single_kol_list, query_params)

        # 检查采集结果
        if platform_data_result and len(platform_data_result) > 0:
            logger.info(f"✅ KOL '{kol_name}' 平台数据采集成功")
            return platform_data_result[0]
        else:
            logger.error(f"❌ KOL '{kol_name}' 平台数据采集失败，未获取到有效数据")
            return None

    except Exception as e:
        logger.exception(f"❌ 采集KOL '{kol_name}' 平台数据时发生异常: {str(e)}")
        return None


def _process_kol_full_update(kol_item: Dict, platform_name: str, stats: Dict, query_params: Dict, lark_client: Lark, log_prefix: str) -> None:
    """
    处理KOL完整更新流程（方案一）

    Args:
        kol_item: 单条KOL标准化数据
        platform_name: 平台名称
        stats: 线程安全统计字典
        query_params: 查询参数
        lark_client: Lark客户端实例
        log_prefix: 日志前缀
    """
    kol_name = kol_item.get('kol_name', '未知KOL')
    
    # 采集单个KOL的平台详细数据
    kol_detail_data = collect_platform_data_single(platform_name, kol_item, query_params)

    if kol_detail_data:
        logger.info(f"{log_prefix} 📊 KOL '{kol_name}' 平台数据采集成功，开始存储到数据库...")

        # 存储到数据库
        stored_kol_data = store_data_to_db_single(kol_detail_data, query_params, log_prefix)

        # 将邮件保存到飞书自动发送邮件的表中
        logger.info(f"{log_prefix} 📊 KOL '{kol_name}' 开始存储到飞书自动发送邮件的表格中...")
        send_to_feishu_single(stored_kol_data, query_params, lark_client)
        
        with stats["lock"]:
            if stored_kol_data:
                stats["success_count"] += 1
                logger.info(f"{log_prefix} ✅ KOL '{kol_name}' 数据存储成功")

                # 邮箱计数
                if stored_kol_data.get("email"):
                    stats["email_count"] += 1
                    logger.info(f"{log_prefix} 📧 KOL '{kol_name}' 包含有效邮箱")

            else:
                stats["failed_count"] += 1
                logger.warning(f"{log_prefix} ❌ KOL '{kol_name}' 数据存储失败")
    else:
        with stats["lock"]:
            stats["failed_count"] += 1
        logger.warning(f"{log_prefix} ❌ KOL '{kol_name}' 平台数据采集失败")


def _process_kol_partial_update(existing_kol: Dict, kol_item: Dict, stats: Dict, query_params: Dict, lark_client: Lark, log_prefix: str) -> None:
    """
    处理KOL部分更新流程（方案二）

    Args:
        existing_kol: 数据库中已存在的KOL数据
        kol_item: 单条KOL标准化数据
        stats: 线程安全统计字典
        query_params: 查询参数
        lark_client: Lark客户端实例
        log_prefix: 日志前缀
    """
    kol_name = kol_item.get('kol_name', '未知KOL')
    current_project_code = query_params.get('project_code', '')
    
    logger.info(f"{log_prefix} 🔄 开始对KOL '{kol_name}' 执行部分更新...")

    # 获取现有的project_codes和tag_names，并添加新的
    existing_project_codes = existing_kol.get("project_codes", [])
    existing_tag_names = existing_kol.get("tag_names", [])
    
    # 添加新的project_code（如果不存在）
    new_project_codes = existing_project_codes.copy()
    if current_project_code not in new_project_codes:
        new_project_codes.append(current_project_code)
    
    # 添加新的tag_names（如果不存在）
    new_tag_names = existing_tag_names.copy()
    current_tag_names = query_params.get("tag_names", [])
    for tag in current_tag_names:
        if tag not in new_tag_names:
            new_tag_names.append(tag)

    # 构建部分更新请求体，只更新三个字段
    api_request_body = {
        "kol_id": existing_kol["kol_id"],
        "kol_name": existing_kol["kol_name"],
        "project_codes": new_project_codes,
        "tag_names": new_tag_names,
        "filter_details": {
            "filter_name": query_params.get("filter_name", ""),
            "project_code": current_project_code,
            "filter_body": query_params.get("filter_body", {})
        }
    }

    logger.debug(f"{log_prefix} 📤 正在调用KOL API进行部分更新...")
    logger.debug(f"{log_prefix} 📋 更新内容: project_codes={new_project_codes}, tag_names={new_tag_names}")

    try:
        # 调用KOL API进行部分更新
        api_response = requests.post(
            f"{base_url}/api/v1/kol",
            json=api_request_body,
            timeout=30
        )

        if api_response.status_code == 200:
            logger.info(f"{log_prefix} ✅ KOL '{kol_name}' 部分更新成功")
            
            # 检查邮箱是否为空，如果不为空则发送到飞书表格
            if existing_kol.get("email"):
                logger.info(f"{log_prefix} 📧 KOL '{kol_name}' 有邮箱，发送到飞书邮件表格")
                send_to_feishu_single(existing_kol, query_params, lark_client)
                
                with stats["lock"]:
                    stats["email_count"] += 1
                    logger.info(f"{log_prefix} 📧 KOL '{kol_name}' 包含有效邮箱")
            else:
                logger.info(f"{log_prefix} 📧 KOL '{kol_name}' 邮箱为空，跳过飞书邮件发送")
            
            with stats["lock"]:
                stats["success_count"] += 1
        else:
            logger.error(f"{log_prefix} ❌ KOL '{kol_name}' 部分更新失败: {api_response.status_code} - {api_response.text}")
            with stats["lock"]:
                stats["failed_count"] += 1
                
    except Exception as e:
        logger.exception(f"{log_prefix} ❌ KOL '{kol_name}' 部分更新时发生异常: {str(e)}")
        with stats["lock"]:
            stats["failed_count"] += 1


def process_single_kol(kol_item: Dict, platform_name: str, total_count: int, stats: Dict, query_params: Dict,
                       qps_controller: 'QPSController', lark_client: Lark, existing_kol_data: Dict[str, Dict] = None) -> None:
    """
    处理单条KOL数据，带QPS控制和线程安全统计。

    Args:
        kol_item: 单条KOL标准化数据
        platform_name: 平台名称
        total_count: 总任务数
        stats: 线程安全统计字典
        query_params: 查询参数
        qps_controller: QPS控制器实例
        lark_client: Lark客户端实例
        existing_kol_data: 已存在的KOL数据字典

    Returns:
        None
    """
    kol_name = kol_item.get('kol_name', '未知KOL')
    kol_id = kol_item.get('kol_id', '未知ID')
    current_project_code = query_params.get('project_code', '')
    
    # 获取当前线程ID
    thread_id = threading.current_thread().ident

    # 日志前缀，包含线程ID和KOL ID
    log_prefix = f"[TID:{thread_id}][KOL:{kol_id}]"

    logger.info(f"{log_prefix} 🎯 线程任务开始: {kol_name}")

    # QPS控制：每秒最多10次
    try:
        logger.debug(f"{log_prefix} ⏱️ 进入QPS控制器: {kol_name}")
        with qps_controller:
            logger.info(f"{log_prefix} 🔄 开始处理KOL: {kol_name}")

            # 检查KOL是否已存在
            existing_kol = existing_kol_data.get(kol_id) if existing_kol_data else None
            
            if existing_kol:
                logger.info(f"{log_prefix} 📋 KOL已存在于数据库中，检查项目重复性...")
                
                # 检查是否在当前项目中已存在（重复数据）
                existing_project_codes = existing_kol.get("project_codes", [])
                if current_project_code in existing_project_codes:
                    logger.info(f"{log_prefix} ⏭️ KOL在当前项目 '{current_project_code}' 中已存在，跳过处理")
                    with stats["lock"]:
                        stats["skipped_count"] += 1
                    return None
                
                # 不是重复数据，根据settings.UPDATE决定处理方式
                logger.info(f"{log_prefix} 🔄 KOL在当前项目中不存在，根据settings.UPDATE决定处理方式")
                
                if settings.UPDATE:
                    # 方案一：完整更新，走正常流程
                    # TODO 需要调用两个接口，一个更新一个新增
                    logger.info(f"{log_prefix} 🔄 settings.UPDATE=True，执行完整更新流程")
                    _process_kol_full_update(kol_item, platform_name, stats, query_params, lark_client, log_prefix)
                else:
                    # 方案二：部分更新，只更新项目相关字段
                    logger.info(f"{log_prefix} 🔄 settings.UPDATE=False，执行部分更新流程")
                    _process_kol_partial_update(existing_kol, kol_item, stats, query_params, lark_client, log_prefix)
            else:
                # 新KOL，走正常流程
                logger.info(f"{log_prefix} 🆕 KOL为新记录，执行完整处理流程")
                _process_kol_full_update(kol_item, platform_name, stats, query_params, lark_client, log_prefix)

            # 更新处理计数
            with stats["lock"]:
                stats["processed_count"] += 1

    except Exception as e:
        logger.exception(f"{log_prefix} ❌ 处理KOL时发生异常: {str(e)}")
        with stats["lock"]:
            stats["failed_count"] += 1
    finally:
        # 进度日志输出
        with stats["lock"]:
            current_processed_count = stats["processed_count"]
            # 每处理1条就输出进度
            logger.info(f"{log_prefix} ✅ 已完成第 {current_processed_count+1} 条，共 {total_count} 条")

    return None


class QPSController:
    """
    QPS控制器，确保每秒最多N次操作，防止API限流。
    """

    def __init__(self, max_qps: int):
        """
        初始化QPS控制器

        Args:
            max_qps: 最大每秒请求数
        """
        self.max_qps = max_qps
        self.thread_lock = threading.Lock()
        self.last_reset_timestamp = time.time()
        self.request_counter = 0
        logger.debug(f"⏱️ QPS控制器初始化完成，限制: {max_qps} 次/秒")

    def __enter__(self):
        """进入QPS控制上下文"""
        max_retries = 1000  # 添加最大重试次数，防止死循环
        retry_count = 0

        while retry_count < max_retries:
            with self.thread_lock:
                current_timestamp = time.time()

                # 每秒重置计数器
                if current_timestamp - self.last_reset_timestamp >= 1:
                    self.last_reset_timestamp = current_timestamp
                    self.request_counter = 0
                    logger.debug("🔄 QPS计数器已重置")

                # 检查是否还有可用配额
                if self.request_counter < self.max_qps:
                    self.request_counter += 1
                    return

            # 等待10ms后重试
            time.sleep(0.01)
            retry_count += 1

        # 如果重试次数过多，记录警告但继续执行
        logger.warning("⚠️ QPS控制器重试次数过多，跳过限制继续执行")
        return

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出QPS控制上下文"""
        pass


def store_data_to_db_single(kol_detail_item: Dict, query_params: Dict, log_prefix: str = "") -> Optional[Dict]:
    """
    将单个KOL数据存储到数据库，通过API接口实现

    Args:
        kol_detail_item: 单个KOL的详细数据
        query_params: 查询参数
        log_prefix: 日志前缀

    Returns:
        Optional[Dict]: 存储后的数据，失败则返回None
    """
    kol_name = kol_detail_item.get('kol_name', '未知KOL')
    kol_id = kol_detail_item.get('kol_id', '未知ID')

    logger.info(f"{log_prefix} 💾 开始将KOL数据存储到数据库: {kol_name}")

    try:

        # 使用大模型提取邮箱和关键词
        logger.debug(f"{log_prefix} 🤖 正在使用AI提取邮箱和关键词...")
        extracted_keywords, extracted_email = email_and_keywords_from_bio(kol_detail_item.get("bio", ""))
        logger.debug(f"{log_prefix} 📧 提取到邮箱: {extracted_email if extracted_email else '无'}")

        # 根据粉丝数判断KOL等级
        followers_count = kol_detail_item.get("followers_count", 0)
        kol_level = None
        if followers_count is not None:
            if followers_count < 10000:
                kol_level = "Nano 1k～10k"
            elif 10000 <= followers_count < 50000:
                kol_level = "Micro 10k～50k"
            elif followers_count >= 50000:
                kol_level = "Mid-tier 50k～500k"

        logger.debug(f"{log_prefix} 📊 KOL等级: {kol_level} (粉丝数: {followers_count})")

        # 计算视频统计数据
        logger.debug(f"{log_prefix} 📹 正在计算视频统计数据...")
        mean_views_k, median_views_k = compute_view_mean_and_median_k(videos=kol_detail_item.get("videos", []))

        # 构建API请求体
        api_request_body = {
            "kol_name": kol_detail_item.get("kol_name", ""),
            "username": kol_detail_item.get("username", ""),
            "email": extracted_email,
            "bio": kol_detail_item.get("bio", ""),
            "account_link": kol_detail_item.get("url", ""),
            "followers_k": kol_detail_item.get("followers_count", 0) / 1000 if kol_detail_item.get(
                "followers_count") else 0,
            "likes_k": kol_detail_item.get("likes_count", 0) / 1000 if kol_detail_item.get("likes_count") else 0,
            "platform": kol_detail_item.get("platform", ""),
            "source": query_params.get("source", ""),
            "mean_views_k": mean_views_k,
            "median_views_k": median_views_k,
            "engagement_rate": kol_detail_item.get("engagement_rate", 0),
            "average_views_k": kol_detail_item.get("averageViews", 0) / 1000 if kol_detail_item.get(
                "averageViews") else 0,
            "level": kol_level,
            "keywords_ai": extracted_keywords,
            "kol_id": f"{kol_detail_item.get('kol_id', '')}",
            "tag_names": query_params.get("tag_names", []),
            "filter_details": {
                "filter_name": query_params.get("filter_name", ""),
                "project_code": query_params.get("project_code", ""),
                "filter_body": query_params.get("filter_body", {})
            }
        }

        logger.debug(f"{log_prefix} 📤 正在调用KOL API存储数据...")

        # 调用KOL API
        api_response = requests.post(
            f"{base_url}/api/v1/kol",
            json=api_request_body,
            timeout=30
        )

        # 检查API响应状态
        if api_response.status_code == 200:
            api_result = api_response.json()
            logger.info(f"{log_prefix} ✅ KOL数据存储成功: {api_request_body['kol_id']}")

            # 处理视频数据存储
            if kol_detail_item.get("videos"):
                logger.debug(f"{log_prefix} 📹 开始处理 {len(kol_detail_item['videos'])} 个视频数据...")
                videos_list = kol_detail_item["videos"]

                for video_index, video_item in enumerate(videos_list, 1):
                    # 构建视频数据请求体
                    video_request_body = {
                        "kol_id": api_request_body["kol_id"],
                        "play_count": video_item.get("play_count", 0),
                        "is_pinned": video_item.get("is_pinned", False),
                        "share_url": video_item.get("share_url", ""),
                        "desc": video_item.get("desc", ""),
                        "desc_language": video_item.get("desc_language", ""),
                        "video_url": video_item.get("video_url", ""),
                        "music_url": video_item.get("music_url", ""),
                        "likes_count": video_item.get("likes_count", 0),
                        "comments_count": video_item.get("comments_count", 0),
                        "shares_count": video_item.get("shares_count", 0),
                        "collect_count": video_item.get("collect_count", 0),
                        "platform": kol_detail_item.get("platform", ""),
                        "hashtags": video_item.get("hashtags", []),
                        "create_time": video_item.get("create_time"),
                        "video_id": video_item.get("video_id", "")
                    }

                    # 检查视频ID是否有效
                    if not video_request_body["video_id"]:
                        logger.warning(f"{log_prefix} ⚠️ 跳过无效视频ID的视频数据 (视频 {video_index})")
                        continue

                    # 调用视频API
                    video_response = requests.post(
                        f"{base_url}/api/v1/videos",
                        json=convert_datetime(video_request_body),
                        timeout=30
                    )

                    if video_response.status_code == 200:
                        logger.debug(f"{log_prefix} ✅ 视频数据存储成功: {video_request_body['video_id']} (视频 {video_index})")
                    else:
                        logger.error(
                            f"{log_prefix} ❌ 视频数据存储失败: {video_response.status_code} - {video_response.text} (视频 {video_index})")

            return api_result
        else:
            logger.error(f"{log_prefix} ❌ KOL数据存储失败: {api_response.status_code} - {api_response.text}")
            return None

    except Exception as e:
        logger.exception(f"{log_prefix} ❌ 存储KOL数据到数据库时发生异常: {str(e)}, KOL: {kol_name}")
        return None


def convert_datetime(obj):
    if isinstance(obj, dict):
        return {k: convert_datetime(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_datetime(i) for i in obj]
    elif isinstance(obj, datetime.datetime):
        return obj.isoformat()
    return obj


def send_to_feishu_single(item: Dict, query_params: Dict, lark_client: Lark) -> bool:
    """
    将单个KOL数据发送到飞书邮箱表

    Args:
        item: 单个KOL的详细数据（可以是API返回结果或数据库查询结果）
        query_params: 查询参数
        lark_client: Lark客户端实例

    Returns:
        是否成功发送
    """
    logger.info(f"开始将KOL数据发送到邮箱表: {item.get('kol_name', '')}")

    try:
        # 检查是否有邮箱
        if not item.get("email"):
            logger.warning(f"KOL没有邮箱，跳过发送: {item.get('kol_name', '')}")
            return False

        # 获取飞书配置 https://laientech.feishu.cn/wiki/KNnaw2wdPiwbIDk1ZlDc2AX6nIc?table=tblUdyWvC0X5Sg9k&view=vewTEKwd7L
        # https://laientech.feishu.cn/wiki/KNnaw2wdPiwbIDk1ZlDc2AX6nIc?table=tblaInulGyWuAoCk&view=vewTEKwd7L test
        app_token = 'Mo7obvrgtaIMqRsZkdwc6UUFnCg'
        table_id = 'tblUdyWvC0X5Sg9k'
        
        # 获取KOL ID - 支持多种字段名
        kol_id = item.get("kol_name") or item.get("kol_id", "")
        if not kol_id:
            logger.error("KOL ID为空，无法发送数据")
            return False

        # 构建精确查询条件，只查询特定KOL ID
        search_params = {
            "filter": {
                "conjunction": "and",
                "conditions": [
                    {
                        "field_name": "KOL ID",
                        "operator": "is",
                        "value": [kol_id]
                    }
                ]
            }
        }

        # 精确查询特定KOL ID的记录
        matching_records = lark_client.search_table_record(app_token, table_id, search_params)

        # 检查是否找到匹配记录
        if matching_records and "items" in matching_records and len(matching_records["items"]) > 0:
            logger.info(f"KOL ID '{kol_id}' 已存在于邮件表，跳过发送")
            return True  # 已存在，返回成功

        # 格式化数据为飞书表格所需格式
        # 支持从数据库查询结果中获取数据
        record = {
            "KOL ID": kol_id,
            "Email": item.get("email", ""),
            "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
            "KOL Name": item.get("username", ""),
            "Template": query_params.get("template", ""),
            "App Code": query_params.get("project_code", ""),
        }

        # 创建新记录
        result = lark_client.batch_create_table_records(app_token, table_id, [record])

        if result:
            logger.info(f"成功将KOL '{kol_id}' 发送到自动发邮件飞书表")
            return True
        else:
            logger.error(f"发送KOL '{kol_id}' 到自动发邮件飞书表失败")
            return False

    except Exception as e:
        logger.exception(f"发送单条KOL数据到飞书邮箱表时发生错误: {str(e)}, KOL: {item.get('kol_name', '')}")
        return False


def send_slack_message(channel_id: str, message_text: str) -> None:
    """
    发送Slack消息通知

    Args:
        channel_id: Slack频道ID
        message_text: 消息内容
    """
    request_body = {
        "channel": channel_id,
        "blocks": [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": message_text
                }
            }
        ]
    }

    headers = {
        "Authorization": "Bearer *******************************************************"
    }

    try:
        response = requests.post(
            "https://slack.com/api/chat.postMessage",
            headers=headers,
            json=request_body,
            timeout=10
        )
        response.raise_for_status()
        logger.debug(f"📢 Slack消息发送成功: {message_text[:50]}...")
    except Exception as e:
        logger.error(f"❌ Slack消息发送失败: {str(e)}")


if __name__ == "__main__":
    # 命令行参数解析
    argument_parser = argparse.ArgumentParser(description='KOL数据采集工具V2')
    argument_parser.add_argument(
        '--channel_id',
        type=str,
        default="U05PD11UDPW",
        help='Slack频道ID，用于发送通知消息'
    )
    parsed_args = argument_parser.parse_args()

    # 获取频道ID参数
    slack_channel_id = parsed_args.channel_id
    logger.info(f"🚀 KOL数据采集工具V2启动，Slack频道ID: {slack_channel_id}")

    # 初始化Lark客户端
    lark_client_instance = Lark()

    # 执行数据采集任务
    for execution_round in range(2):
        logger.info(f"🔄 开始第 {execution_round + 1} 轮数据采集任务")

        # 查询待处理的飞书表格记录
        table_records = lark_client_instance.search_table_all_records(
            "PU8Qb8atYaXdylsxolOcSHrgn4e",
            "tbl52mnkqPC24Kya",
            search_params={
                "field_names": [
                    "Cookie",
                    "Filter Criteria",
                    "Source",
                    "Platform",
                    "Filter Name",
                    "Project Code",
                    "Auto Send",
                    "Email Template",
                    "High Pontential",
                    "high potential存储表",
                    "high potential去重表"
                ],
                "filter": {
                    "conjunction": "and",
                    "conditions": [
                        {
                            "field_name": "Cookie",
                            "operator": "isNotEmpty",
                            "value": []
                        },
                        {
                            "field_name": "Date",
                            "operator": "is",
                            "value": ["Today"]
                        },
                        {
                            "field_name": "点击",
                            "operator": "is",
                            "value": ["false"]
                        }
                    ]
                },
                "automatic_fields": False
            },
            page_size=500
        )

        processed_tasks_list = []

        # 处理每条记录
        for table_record in table_records:
            record_fields = table_record["fields"]

            # 拼接过滤条件
            filter_criteria_text = "".join([fc["text"] for fc in record_fields["Filter Criteria"]])

            # 拼接Cookie信息
            cookie_text = "; ".join([
                cookie_item["text"].strip().rstrip(";")
                for cookie_item in record_fields["Cookie"]
                if cookie_item.get("type") == "text"
            ])

            # 构建查询参数
            task_query_params = {
                "source": record_fields.get("Source", ""),
                "platform": record_fields.get("Platform", ""),
                "filter_name": record_fields.get("Filter Name", [{}])[0].get("text", ""),
                "filter_body": json.loads(filter_criteria_text),
                "cookie": cookie_text,
                "project_code": record_fields.get("Project Code", ""),
                "auto_send": record_fields.get("Auto Send", ""),
                "template": record_fields.get("Email Template", ""),
                "high_potential": record_fields.get("High Pontential", ""),
                "table_save": record_fields.get("high potential存储表", {}).get("link", ""),
                "table_duplicate": record_fields.get("high potential去重表", "")
            }

            processed_tasks_list.append({
                "query_params": task_query_params,
                "record_id": table_record["record_id"]
            })

        # 执行数据采集任务
        logger.info(f"📋 本轮共有 {len(processed_tasks_list)} 个任务待执行")

        for task_index, task_item in enumerate(processed_tasks_list, 1):
            # 检查项目标签配置
            project_code = task_item['query_params']['project_code']
            tag_api_url = f"{base_url}/api/v1/project-tag-associations/project/{project_code}/tags"

            try:
                tag_response = requests.get(tag_api_url, timeout=10)
                tag_info = tag_response.json()
                project_tags = tag_info.get("tags", [])

                if not project_tags:
                    logger.warning(f"⚠️ 项目 '{project_code}' 没有设置标签，请先设置默认标签")
                    send_slack_message(
                        slack_channel_id,
                        f"*项目 {project_code} 没有设置默认标签，请先设置默认标签*"
                    )
                    continue

                tag_names_list = [tag["name"] for tag in project_tags]
                task_item['query_params']['tag_names'] = tag_names_list
                logger.info(f"🏷️ 项目 '{project_code}' 标签列表: {tag_names_list}")

            except Exception as e:
                logger.error(f"❌ 获取项目 '{project_code}' 标签失败: {str(e)}")
                continue

            # TODO: 临时过滤条件，用于测试特定任务
            # if task_item['query_params']['filter_name'] != "TTone-animetok-keyword search-10k-US-141":
            #     continue

            # 发送任务开始通知
            task_start_message = f"*开始执行:* 🚀 {'=' * 5} {task_item['query_params']['filter_name']} {'=' * 5}"
            send_slack_message(slack_channel_id, task_start_message)
            logger.info(f"🚀 开始执行任务 {task_index}: {task_item['query_params']['filter_name']}")

            # 执行数据采集
            execution_result = collect_kol_data_v2(
                task_item['query_params'],
                lark_client_instance,
                task_item['record_id']
            )

            # 发送执行结果通知
            result_message = f"*执行结果:* :rocket: {'=' * 5} {execution_result['message']} {'=' * 5}"
            send_slack_message(slack_channel_id, result_message)
            logger.info(f"📊 任务 {task_index} 执行结果: {execution_result['message']}")

            # 发送任务结束通知
            task_end_message = f"*执行结束:* :🛬: {'=' * 5} {task_item['query_params']['filter_name']} {'=' * 5}\n"
            send_slack_message(slack_channel_id, task_end_message)
            logger.info(f"✅ 任务 {task_index} 执行结束: {task_item['query_params']['filter_name']}\n")

        # 发送本轮完成通知
        round_completion_message = f"*执行结束:* :🛬: 第 {execution_round + 1} 轮任务全部执行完毕\n"
        send_slack_message(slack_channel_id, round_completion_message)
        logger.info(f"🎉 第 {execution_round + 1} 轮任务全部执行完毕")
