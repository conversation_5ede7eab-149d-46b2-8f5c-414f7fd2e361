#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project : kol-python
<AUTHOR> 小林同学
@Date    : 2025/3/25 下午6:16 
@Docs    : 通过查询条件从 TikTokOne 聚合平台获取 kol 数据
'''

import time
import json
import random
import requests
from typing import Any, Dict, List, Optional
from loguru import logger


class TiktokOneError(Exception):
    """TiktokOne API 异常基类"""
    pass


class TiktokOneClient:
    """TikTok Creator平台客户端"""

    def __init__(
            self,
            cookie: str,
            request_body: dict,
            url: str,
            base_delay: float = 1.0,
            requests_per_minute: int = 60,
    ) -> None:
        """初始化客户端"""
        self.cookie = cookie
        self.request_body = request_body
        self.base_url = url
        self.default_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'agw-js-conv': 'str',
            'content-type': 'application/json',
            'origin': self.base_url,
            'priority': 'u=1, i',
            'referer': f"{self.base_url}/creative/creator/explore",
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'x-csrftoken': '4MrxJcg4ayp8VanSiTw7PlslJgpECXMI'
        }
        self.base_delay = base_delay
        self.requests_per_minute = requests_per_minute
        self._last_request_time = 0
        self._session = requests.Session()

    def fetch_creators(self, page: int = 1, max_retries: int = 3, retry_delay: float = 5.0) -> Dict[str, Any]:
        """获取创作者列表（添加重试机制）"""
        for attempt in range(max_retries):
            self._rate_limit_control()
            
            url = f"{self.base_url}/CreativeOne/MatchMaking/QueryCreatorSquare"

            try:
                cookie_dict = dict(item.split("=", 1) for item in self.cookie.split("; ") if "=" in item)
                request_headers = self.default_headers.copy()
                if "csrftoken" in cookie_dict:
                    request_headers["x-csrftoken"] = cookie_dict["csrftoken"]

                payload = self.request_body.copy()
                payload["page"] = page

                response = self._session.post(
                    url,
                    json=payload,
                    headers=request_headers,
                    cookies=cookie_dict,
                    timeout=30,
                )
                response.raise_for_status()
                data = response.json()

                if data.get("baseResp", {}).get("StatusCode") != 0:
                    error_msg = f"API错误: {data.get('baseResp', {}).get('StatusMessage', '未知错误')}"
                    logger.warning(f"第{attempt+1}次尝试失败: {error_msg}")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    raise TiktokOneError(error_msg)

                return data

            except (requests.exceptions.RequestException, json.JSONDecodeError) as e:
                logger.warning(f"第{attempt+1}次尝试失败: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                if isinstance(e, requests.exceptions.RequestException):
                    error_msg = f"HTTP请求失败: {str(e)}"
                else:
                    error_msg = f"响应数据解析失败: {str(e)}"
                raise TiktokOneError(error_msg)
        
        raise TiktokOneError("所有重试尝试均失败")

    def _rate_limit_control(self) -> None:
        """速率限制控制"""
        now = time.time()
        elapsed = now - self._last_request_time
        min_interval = 60.0 / self.requests_per_minute

        if elapsed < min_interval:
            wait_time = min_interval - elapsed
            time.sleep(wait_time)

        self._last_request_time = time.time()

    def get_all_creators(self, max_pages: int = 100, min_delay: float = 2.0, max_delay: float = 5.0) -> List[Dict[str, Any]]:
        """获取所有符合条件的创作者（优化版）"""
        page = 1
        creators = []
        consecutive_empty = 0
        max_consecutive_empty = 3  # 连续空结果阈值
        page_retry_limit = 3  # 每页重试次数
        total_pages = None  # 初始化总页数为None

        try:
            while page <= (total_pages or max_pages):
                # 对当前页进行多次尝试
                page_retry_count = 0
                page_data = []
                
                while page_retry_count < page_retry_limit and not page_data:
                    response = self.fetch_creators(page=page)
                    page_data = response.get("creators", [])
                    
                    # 获取第一页时计算总页数
                    if page == 1 and total_pages is None and "pagination" in response:
                        total_count = response.get("pagination", {}).get("totalCount", 0)
                        limit = response.get("pagination", {}).get("limit", 24)
                        if total_count > 0 and limit > 0:
                            total_pages = (total_count + limit - 1) // limit  # 向上取整
                            logger.info(f"检测到总数据 {total_count} 条，每页 {limit} 条，共 {total_pages} 页")
                            if total_pages < max_pages:
                                logger.info(f"实际页数({total_pages})小于最大页数限制({max_pages})，将使用实际页数")
                            else:
                                total_pages = max_pages
                                logger.info(f"实际页数超过最大限制，将使用最大页数限制: {max_pages}页")
                    
                    if not page_data:
                        page_retry_count += 1
                        if page_retry_count < page_retry_limit:
                            retry_wait = random.uniform(5.0, 10.0)  # 页面重试使用更长的等待时间
                            logger.warning(f"第 {page} 页返回空数据，第 {page_retry_count} 次重试，等待 {retry_wait:.2f} 秒后重试")
                            time.sleep(retry_wait)
                        else:
                            logger.warning(f"第 {page} 页在重试 {page_retry_limit} 次后仍然返回空数据")
                
                # 如果所有重试后仍然没有数据
                if not page_data:
                    consecutive_empty += 1
                    logger.warning(f"第 {page} 页返回空数据，这是第 {consecutive_empty} 次连续空结果（已重试 {page_retry_limit} 次）")
                    
                    if consecutive_empty >= max_consecutive_empty:
                        logger.info(f"连续 {max_consecutive_empty} 次获取空结果，结束查询")
                        break
                    
                    # 尝试下一页，或许是临时问题
                    page += 1
                    # 遇到空结果，增加延迟
                    time.sleep(random.uniform(max_delay, max_delay * 2))
                    continue
                
                # 重置连续空结果计数
                consecutive_empty = 0
                
                creators.extend(page_data)
                logger.info(f"第 {page} 页, 获取到 {len(page_data)} 条数据")

                if not response.get("pagination", {}).get("hasMore", False):
                    logger.info("平台返回无更多数据标志，结束查询")
                    break

                page += 1
                # 随机延迟，避免被识别为机器人
                time.sleep(random.uniform(min_delay, max_delay))

            return creators

        except Exception as e:
            logger.error(f"获取所有创作者失败: {str(e)}")
            # 即使出错，也返回已获取的数据
            if creators:
                logger.info(f"返回已获取的 {len(creators)} 条数据")
                return creators
            raise TiktokOneError(str(e))


def ttone_search(
        cookie: str,
        request_body: dict,
        url: str = "https://ads.tiktok.com",
        base_delay: float = 2.0,  # 增加基础延迟
        requests_per_minute: int = 20,  # 降低请求频率
) -> List:
    """TTOne 平台数据处理主函数"""
    logger.info(f"开始处理 TTOne 数据")

    try:
        if request_body.get("dataVDCRegion") == 2:
            url = "https://ads-useast2a.tiktok.com"
        client = TiktokOneClient(
            cookie=cookie,
            request_body=request_body,
            url=url,
            base_delay=base_delay,
            requests_per_minute=requests_per_minute
        )

        creators = client.get_all_creators(
            max_pages=200,  # 设置最大页数限制
            min_delay=2.0,  # 最小延迟
            max_delay=5.0   # 最大延迟
        )
        return creators
    except Exception as e:
        logger.error(str(e))
        return []


# 示例使用方法
if __name__ == '__main__':
    # 示例配置
    cookie = 'tt_csrf_token=AUL4jfoi-NLkbvRCu-1r6dRnLmZAksSyT4e4; s_v_web_id=verify_m7ldilth_AykKqVo8_0dBH_49Op_AYBX_TGNqWM2T71bD; tta_attr_id_mirror=0.1742365968.7483404848235282433; _ga=GA1.1.1254120416.1742365975; FPID=FPID2.2.ZgkPpczbUjkqpUeLRbaIYH%2FS0YsxLAVwqVod1%2BpYcRw%3D.1742365975; FPAU=1.2.800406463.1742365975; _fbp=fb.1.1742365975019.1353528986; _tt_enable_cookie=1; d_ticket_ads=46a64ca93c4877b44561ea6afe6dd9471c952; tt_chain_token=vsN9eBC2fNRNfFAHvhXhdw==; multi_sids=7475275543664788487%3A25c161fb3785277696c91b8eeb54e680; cmpl_token=AgQQAPOqF-RO0reDbSfafN0-_bv8v6tTP6nZYNi4Hw; uid_tt=33fc3c9276770d465c08945bee6d7528b0c0fbf72f528481e9cfb54d069b1920; uid_tt_ss=33fc3c9276770d465c08945bee6d7528b0c0fbf72f528481e9cfb54d069b1920; sid_tt=25c161fb3785277696c91b8eeb54e680; sessionid=25c161fb3785277696c91b8eeb54e680; sessionid_ss=25c161fb3785277696c91b8eeb54e680; store-idc=alisg; store-country-code=jp; store-country-code-src=uid; tt-target-idc=alisg; tt-target-idc-sign=apGYridyzGXEDSlTqIQoWagyIHa9KeDnhIGzIn4m7oH7d0cCO-n_C0bIFBQQn9ApZXbRVh7tJOVm8Gigu4yyhES4JtXZv0bhtDWBkNkkzwiddzX9oAe1X7yjfFJSa1P4kCEDWR1XfqheFE2EqYTv_T3JgZYg17SsX01Ivlougt0fLblsIYmJRQvuheSLUqu3f2096I7KsZGb1LTU7I2DINApfY6vdpi7PxtQQr1DAQhSk0DlBBXoEkKeqzzGE67FQvyCS2pDFi8fuH9o-OmdEthE_xsAVn90okgXuQybjCroi8Ht_9PNGxw_l2vpEsMxIFVMa6PyXWN65amJC1XeATxPyXbAJv6roHxdmvyw7DCP3EJ-Gn6_ShCse0x10jkhZQ01yN2ycv-JzWxKGNphuI_Uvdy_N6cl0JL3PuDpm_nm8FMA_J9OPpMSngVzQ3fjoRjJlpmfTAe55gfkR7yeFo-WvKIV1cd_wa6xiQxuKiarLrUBOhEUpuMotzPF3nX7; sid_guard=25c161fb3785277696c91b8eeb54e680%7C1742452560%7C15551991%7CTue%2C+16-Sep-2025+06%3A35%3A51+GMT; sid_ucp_v1=1.0.0-KDY3MGI2NWIxM2VjNzQ2OTk4ZmM3MTMwZjBlNzBiMmRhMmE3MzE1YjkKGQiHiLiShfDg3mcQ0O7uvgYYsws4CEASSAQQAxoDc2cxIiAyNWMxNjFmYjM3ODUyNzc2OTZjOTFiOGVlYjU0ZTY4MA; ssid_ucp_v1=1.0.0-KDY3MGI2NWIxM2VjNzQ2OTk4ZmM3MTMwZjBlNzBiMmRhMmE3MzE1YjkKGQiHiLiShfDg3mcQ0O7uvgYYsws4CEASSAQQAxoDc2cxIiAyNWMxNjFmYjM3ODUyNzc2OTZjOTFiOGVlYjU0ZTY4MA; _ga_ER02CH5NW5=GS1.1.1743488214.1.1.1743488287.0.0.1749825818; passport_csrf_token=e5e38456bf86315e00f7e7cde523da58; passport_csrf_token_default=e5e38456bf86315e00f7e7cde523da58; tiktok_webapp_lang=zh-Hans; _ttp=2w4ALNg1aHgVJ9IxyXzOLIhyFzQ.tt.1; ttcsid=1746431025359::51w4xZQMWRabZ76GoU1u.1.1746431025633; ttcsid_C97F14JC77U63IDI7U40=1746609696657::a57E-w1i3RCrNnZZlvwn.2.1746609749897; sso_uid_tt_ads=229acc20ecde5fad4e30799d565c572eae156ad141869b8c486588f06f02786c; sso_uid_tt_ss_ads=229acc20ecde5fad4e30799d565c572eae156ad141869b8c486588f06f02786c; sso_user_ads=030f899f2adc5e45656781b7377314a1; sso_user_ss_ads=030f899f2adc5e45656781b7377314a1; sid_ucp_sso_v1_ads=1.0.0-KDY0YmJhZjE3MDNiODE1YmRmZWJkOTI3MDgwNjI2NzkwMzYwM2Y3ODYKGAiBiNO-5pLY7mUQ3szswAYYrww4AUDrBxADGgNteTIiIDAzMGY4OTlmMmFkYzVlNDU2NTY3ODFiNzM3NzMxNGEx; ssid_ucp_sso_v1_ads=1.0.0-KDY0YmJhZjE3MDNiODE1YmRmZWJkOTI3MDgwNjI2NzkwMzYwM2Y3ODYKGAiBiNO-5pLY7mUQ3szswAYYrww4AUDrBxADGgNteTIiIDAzMGY4OTlmMmFkYzVlNDU2NTY3ODFiNzM3NzMxNGEx; uid_tt_ads=107e611e46c1fe536cf6204042c7bd1011206b96bc711db5b38cfc4c79619f17; uid_tt_ss_ads=107e611e46c1fe536cf6204042c7bd1011206b96bc711db5b38cfc4c79619f17; sid_tt_ads=cf88e1c0a1f1af720ba7370c8ef87406; sessionid_ads=cf88e1c0a1f1af720ba7370c8ef87406; sessionid_ss_ads=cf88e1c0a1f1af720ba7370c8ef87406; _ga_HV1FL86553=GS1.1.1746609759.4.0.1746609759.0.0.1363192109; _ga_Y2RSHPPW88=GS1.1.1746609696.3.1.1746609759.0.0.1115020647; s_aio_client_id=7441157856818233360; store-country-sign=MEIEDJQumofdal0pgUiQOgQgVPmAODXPfqJxC8uWKBQPvlvyA8-ng1OC0orDH7Vlq-oEEAIk5hmvncNMzfg184--Ixg; ttwid=1%7Cia0CEh7GZ-Oy55mG--ljiMtDImlPSDX4ZkF9ZsWDw20%7C1746783120%7Cb69823862022822da5dd7f028222782cd14dfd0ab07a66ded630608d02e9106d; sid_guard_ads=cf88e1c0a1f1af720ba7370c8ef87406%7C1747016957%7C864000%7CThu%2C+22-May-2025+02%3A29%3A17+GMT; sid_ucp_v1_ads=1.0.0-KDc0YzhlYTljNDYwZDYyOGQ0NGQ5NGQ0MDBjMTA2YzY1MjcwNTllYzgKGgiBiNO-5pLY7mUQ_bmFwQYYrwwgDDgBQOsHEAMaA3NnMSIgY2Y4OGUxYzBhMWYxYWY3MjBiYTczNzBjOGVmODc0MDY; ssid_ucp_v1_ads=1.0.0-KDc0YzhlYTljNDYwZDYyOGQ0NGQ5NGQ0MDBjMTA2YzY1MjcwNTllYzgKGgiBiNO-5pLY7mUQ_bmFwQYYrwwgDDgBQOsHEAMaA3NnMSIgY2Y4OGUxYzBhMWYxYWY3MjBiYTczNzBjOGVmODc0MDY; odin_tt=073da046377e57f3f9eec9f1df897fb0385489e3c9bff67d7d82ff00f251763fe8154acfa7387629da4252c8c769c01c; msToken=clAafoWhVe3mx2zShOuB_1DqSh4t44OEVKM3KSlIxQq22Sa0Dc_YBlYh5m6yHUHXBVwSF0d7lNeuQxh9hY_gkv3lLdaaBmhwpZYZQ_WIuB8G05Iiv3FuScT2BqOX8tyGKanrSJK5XsCOC_MkQERUOcBguQ=='
    request_body = {
      "page": 1,
      "limit": 24,
      "query": "weightloss",
      "filterParam": {
        "minFansCnt": 1000,
        "contentLabels": [],
        "industryLabels": [],
        "creatorPriceFilter": {
          "currency": "USD"
        },
        "languages": [
          "en"
        ],
        "minMedianViews": 5000,
        "minEngagementRate": 0.02,
        "maxEngagementRate": 1,
        "storeCountryCodeList": [],
        "subRegions": [],
        "minAudienceDistriGenderFemaleRatio": 0.6
      },
      "sortParam": {
        "sortType": 2,
        "sortField": 1
      },
      "dataVDCRegion": 2,
      "searchType": 6,
      "abParams": "{\"creator_explore_content_v1\":{\"val\":\"2\",\"vid\":\"73772759\"},\"similarity_apac\":{\"val\":1,\"vid\":\"73818847\"}}"
    }
    filter_tag = "fitness_creators"

    result = ttone_search(cookie=cookie, request_body=request_body)
    print(len(result))
