from typing import Dict, List
import cloudscraper
import time
import random
import base64
import zlib
import json

from loguru import logger


class NoxCrawlerService:
    """
    Noxinfluencer爬虫服务类
    用于从noxinfluencer.com获取TikTok用户数据
    """
    
    def __init__(self, cookies: Dict[str, str], filter_data: Dict):
        """
        初始化爬虫服务
        
        Args:
            cookies: 请求cookies字典
        """
        self.cookies = cookies
        self.scraper = cloudscraper.create_scraper()
        self.filter_data = {}
        self.base_url = "https://cn.noxinfluencer.com/ws/v2/instagram/star/search"
        
    def decode_filter(self, encoded_filter: str) -> Dict:
        """
        解码base64 filter字符串为字典
        
        Args:
            encoded_filter: 编码的filter字符串
            
        Returns:
            Dict: 解码后的filter字典
        """
        try:
            # Base64解码
            decoded = base64.b64decode(encoded_filter)
            # 解压缩
            decompressed = zlib.decompress(decoded)
            # JSON解析
            filter_data = json.loads(decompressed.decode('utf-8'))
            return filter_data
        except Exception as e:
            logger.error(f"解码filter失败: {e}")
            return {}
    
    def encode_filter(self, filter_data: Dict) -> str:
        """
        将filter字典编码为base64字符串
        
        Args:
            filter_data: filter字典对象
            
        Returns:
            str: 编码后的base64字符串
        """
        try:
            # 将字典转换为JSON字符串
            json_str = json.dumps(filter_data, separators=(',', ':'))
            # 压缩JSON字符串
            compressed = zlib.compress(json_str.encode('utf-8'))
            # Base64编码
            encoded = base64.b64encode(compressed).decode('utf-8')
            return encoded
        except Exception as e:
            logger.error(f"编码filter失败: {e}")
            return ""
    
    def get_kol_datas(self, max_pages: int = 2) -> List[Dict]:
        with open("/Users/<USER>/prjs/work/KOL-python/app/services/nox_tk_demo.json", "r") as f:
            datas = json.load(f)
            return datas

        """
        获取KOL data列表（alias列表）
        
        Args:
            filter_data: filter字典对象，包含pageNum、pageSize等参数
            max_pages: 最大页数，默认1页
            
        Returns:
            List[str]: KOL data列表（alias列表）
        """
        datas = []
        
        for page in range(1, max_pages + 1):
            try:
                # 更新页码
                current_filter = self.filter_data.copy()
                current_filter['pageNum'] = page
                
                # 编码filter
                encoded_filter = self.encode_filter(current_filter)
                if not encoded_filter:
                    logger.error(f"编码第 {page} 页filter失败")
                    continue
                
                # 生成时间戳
                timestamp = str(int(time.time())) + str(random.randint(1000, 9999))
                
                # 构建请求URL
                url = f"{self.base_url}?p={encoded_filter}&t={timestamp}"
                
                logger.info(f"请求第 {page} 页数据")
                
                # 发送请求
                response = self.scraper.get(url, cookies=self.cookies)
                
                if response.status_code == 200:
                    response_data = response.json()                    
                    page_datas = response_data.get("retDataList", [])
                    
                    datas.extend(page_datas)
                    
                    logger.info(f"第 {page} 页获取到 {len(page_datas)} 个KOL data")
                    
                else:
                    logger.error(f"请求失败，状态码: {response.status_code}")
                    break
                    
            except Exception as e:
                logger.error(f"处理第 {page} 页时发生错误: {e}")
                break
        
        logger.info(f"总共获取到 {len(datas)} 个KOL data")
        
        return datas
    

# 示例使用
if __name__ == "__main__":
    # 配置cookies
    cookies_str = "_ga=GA1.1.486037572.1751436751; cookiePrivacy=true; firstLogin=false; lastLogin=brand; customLanguage=ZH; NSESSID=s%3AswAPZ4TatPVyICjoJgz2pqn7Ao6ocr8z.OHWVQWVwJJXDItJ2UjRL%2FM8lHdRWYXOnOWx1Ena8KWM; _ga_PZTQQRP507=GS2.1.s1751624795$o13$g1$t1751625680$j49$l0$h0"
    
    # 将cookie字符串转换为字典格式
    cookies = {}
    for cookie in cookies_str.split('; '):
        if '=' in cookie:
            key, value = cookie.split('=', 1)
            cookies[key] = value
    
    # 创建爬虫实例
    
    filter_data = {
        "searchWords": "anime,5,weeb,5,otaku,5,animation,5",
        "excludeWords": "",
        "operator": "or",
        "lastActiveTime": 30,
        "followerLanguage": "ja,en,de",
        "estExposureGte": 50000,
        "followerLte": 500000,
        "customCategoryIds": "3973_2",
        "pageNum": 1,
        "pageSize": 100,
        "subSite": "en"
    }
    crawler = NoxCrawlerService(cookies, filter_data)

    
    datas = crawler.get_kol_datas(max_pages=2)
    with open("/Users/<USER>/prjs/work/KOL-python/app/services/nox_ins_demo.json", "w") as f:
            json.dump(datas, f, indent=4)
  

