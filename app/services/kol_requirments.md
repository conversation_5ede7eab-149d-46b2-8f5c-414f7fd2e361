一 KOL业务目标
品牌曝光 (Awareness)

建立信任 (Trust)

促进转化 (Conversion)

详细背景
公司内部正在建设一个面向全公司项目的 KOL 管理平台，业务团队会定期导入外部平台（如 TikTok、Instagram）上的 KOL 数据，并通过平台完成筛选、发送邮件、跟踪回复等动作。平台后期也会支持社媒数据同步、KOL表现跟踪等功能。你作为后端工程师，参与平台的第一期功能设计和开发，需要搭建数据库、设计接口，并思考未来的扩展方向。

二 自动化目标
1. 提效增能
目标：将团队从重复的、低价值的“体力活”中解放出来。

具体实现：

自动化发现：自动抓取和筛选 KOL。

自动化触达：自动获取邮箱并发送个性化邮件。

自动化追踪：自动监控合作作品的数据。

最终结果：让团队能把 80% 的时间花在真正重要的“脑力活”上，比如策略制定、创意沟通、关系维护和复盘优化，而不是复制粘贴。

2. 量化决策
目标：让 KOL 营销的每一个环节都变得透明、可衡量。

具体实现：

过程数据化：清晰地看到联系了多少人、多少人回复、多少人合作。

结果数据化：自动计算每个 KOL 的 ROI（投入产出比）、CPM（千次展示成本）、CPE（单次互动成本）。

最终结果：彻底改变过去“凭感觉”选人的模式。管理者和运营能基于真实数据，清晰地知道“钱花得值不值”以及“下一次该选谁”。

3. 沉淀资产
目标：将所有的 KOL 合作经验和数据，转化为公司可复用的宝贵资产。

具体实现：

建立 KOL 数据库：沉淀所有联系过、合作过的 KOL 信息、历史表现和沟通记录。

建立策略知识库：通过数据分析，总结出哪些类型的 KOL、哪些形式的内容效果最好。

最终结果：即使人员流动，公司的 KOL 营销能力也不会丢失。新项目启动时，可以直接从内部的“优质 KOL 资产库”中快速找到合适人选，大大降低试错成本。

三、 业务流程
整个流程可以设计为一个清晰的四步工作流：

步骤一：海量初筛
执行人：市场/运营专员

使用工具：Modash, TTone, Creable

操作：

在这些外部平台上，根据项目需求（如，美国地区、游戏标签、10-50万粉丝）进行筛选。

从平台上导出一份包含 KOL 用户名（Username/Handle）的候选人名单（CSV/Excel）。

产出：一份包含几十到几百个 KOL 用户名的候选名单。

步骤二：数据拉取与精准分析
执行人：市场/运营专员 & 自动化系统

使用工具：您的自动化系统（后台集成 TikHub）

操作：

运营将第一步的候选名单导入到您的自动化系统中。

系统接收到名单后，自动为每个 KOL 调用 TikHub 的 API，拉取最新的粉丝、互动和简介等数据。

系统自动尝试从简介中提取 Email。

产出：系统内的 KOL 列表被更新，此时每个 KOL 会有两种初始状态：【已找到 Email】或【未找到 Email】。

步骤三：最终决策与触达
路径 A (成功路径): 对于状态为【已找到 Email】的 KOL
执行人：运营专员

操作：直接进入步骤四，进行人工决策和邮件触达。

路径 B (异常处理路径): 对于状态为【未找到 Email】的 KOL
执行人：自动化系统 & 运营专员

使用工具：您的自动化系统（集成 LLM API），NanoInfluencer.ai 插件

操作：

系统自动触发 LLM 分析：

系统将该 KOL 的状态更新为【LLM 分析中】。

自动调用 TikHub API，获取该 KOL 最近 15 个视频的标题/标签。

将视频数据和预设的“产品画像”（例如：“寻找测评类、风格年轻化的科技博主”）一起打包，发送给 LLM API。

系统处理 LLM 结果：

如果 LLM 返回“不符合”：系统自动将该 KOL 状态更新为【已归档-画像不符】。此 KOL 的流程结束，运营无需再投入精力。

如果 LLM 返回“符合”：系统将该 KOL 状态更新为【待二次查找 Email】。

人工二次查找：

运营人员在系统后台的“待二次查找”列表里看到这个 KOL。

运营切换到飞书，使用 NanoInfluencer.ai 插件，根据 KOL 的用户名，自动进行深度查找。

结果回填：

如果在 Nano 中找到了 Email：运营将 Email 复制回您的自动化系统中，并将 KOL 状态手动更新为【已找到 Email】。该 KOL 成功进入步骤四。

如果在 Nano 中仍未找到 Email：运营在系统中将其状态更新为【已归档-无联系方式】。流程结束。

步骤四：合作与效果监控
执行人：市场/运营专员 & 自动化系统

使用工具：您的自动化系统（后台集成 TikHub）

操作：

与 KOL 达成合作后，在系统中将其状态更新为“已合作”。

KOL 发布视频后，将视频链接粘贴到系统中对应的 KOL 条目下。

系统开始自动、定期地通过 TikHub 监控该视频的表现，并更新数据报表。

产出：一个自动更新的合作效果看板，为衡量 ROI 提供实时、准确的数据支持。





需要你实现四个任务来实现kol系统，可落地的1.0版本系统

任务一：深度理解业务背景和业务流程，深度理解当前kol模型设计：路径为：/Users/<USER>/prjs/work/KOL-python/app/models
在此基础上给出任务二、三、四的最佳实践


任务二：请设计KOL数据库结构来支持以下功能需求：
1 **KOL候选池管理:**
    - **需求：** 系统需要能接收并管理从 `Modash`, `TTone`, `Creable` 等平台初筛出的KOL名单（例如，通过导入CSV文件或手动粘贴列表）。
2. **KOL数据自动分析:**
    - **需求：** 输入一个KOL的TikTok/抖音用户名后，系统能**自动调用 `TikHub` 的API**，获取并展示该KOL的最新核心数据，包括：
        - 实时粉丝数、互动率
        - 近期作品列表及其表现（播放、点赞、评论）
        - 个人简介 **Bio** 原文（用于下一步提取邮箱）
3. **联系信息自动提取:**
    - **需求：** 系统能自动扫描从 `TikHub` 获取的个人简介文本，使用正则表达式**自动识别并提取出Email地址**。
    - **如果未提取到 Email 地址**，使用 LLM 分析 kol 前 15 个视频内容或标签，分析是否符合标准；符合标准的 kol 使用 `NanoInfluencer.ai` 进一步提取 email 地址。
4 •多个“项目（Project）”可以独立使用该平台，每个项目都维护自己的 KOL 名单
5 •每个 KOL 至少包含：昵称、平台（TikTok/Instagram）、粉丝数、联系邮箱、国家、标签（可多选）
6 •支持记录每次邮件发送行为，包括：发送时间、发送状态、邮件模版ID、是否成功、失败原因
7 •后续会扩展功能，如邮件打开率跟踪、KOL视频/图文作品同步、回复追踪等
8 ·后续需要根据kol的hashtag来反查kol信息

请列出主要表名、字段名、字段类型、主外键设计以及详细的ER图（mermaid脚本），新生成文件，建表sql和er图脚本放到/Users/<USER>/prjs/work/KOL-python/redo下


任务三：API 设计请基于上面数据库结构，设计以下接口（无需实际编码，只需写出接口说明）：
1.创建或更新某个项目下的 KOL 数据（支持批量）
2.查询某个项目下所有满足筛选条件的 KOL（如：国家 = 日本，标签包含”母婴”）
3.记录一次发送邮件日志
4.查询某个 KOL 最近的邮件发送记录（支持分页）
 每个接口请写出：请求方式、路径、请求参数（简要），响应格式示例（JSON） 新生成md文件放到/Users/<USER>/prjs/work/KOL-python/redo下


 任务四：可扩展性思考当前系统只支持邮件发送，但未来还计划：
•支持业务方自定义字段（如“是否合作过”、“报价范围”等）
•多业务团队使用平台，但数据相互隔离，权限不同


任务五：
1 当前老系统还存在一个问题，由于数据来源都是根据一系列的搜索条件filter来筛选，但是入库的时候没有对filter做细分，比如简单的将filter命名为Modash-dabi related-hashtags-10k-1849
（就是一些来源信息，粉丝量、地区等信息组成的简单字符串）导致根据tag后续反搜kol的时候不准。如何解决这个问题？ 让运营团队给出详细的，最终可以识别到kol身份的tag并入库？ 你有什么好的解决方案吗？ 请你重新考虑tag表设计

2 新设计的kol表中有些明确不需要的字段 帮我删除：
    average_views_k DECIMAL(10,2),
    average_likes_k DECIMAL(10,2),
    average_comments_k DECIMAL(10,2),
    most_used_hashtags JSONB,
    slug,
    creator_id,

3 @/Users/<USER>/kol_info_202507091428.json 是老kol表的实际数据，帮我根据数据库范式的角度以及之前业务字段的角度 帮我查看是否有数据冗余问题以及字段类型不合理的问题
